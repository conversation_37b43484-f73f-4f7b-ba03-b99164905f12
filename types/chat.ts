/**
 * Unified chat types across the entire application
 * Consolidates types from API, persistence, cache, and UI layers
 */

import type { AiStepType, AiMessageRole } from '@prisma/client'

// ─────────────────────────────────────────────
// Core Message Types
// ─────────────────────────────────────────────

/**
 * Message content supporting both string and multimodal formats
 * Compatible with Vercel AI SDK v2
 */
export type MessageContent =
  | string
  | {
      type: 'text'
      text: string
    }[]

/**
 * Core chat message type used across all layers
 */
export type ChatMessage = {
  role: 'user' | 'assistant' | 'system'
  content: MessageContent
  thinking?: string
  toolCalls?: ToolCall[]
}

/**
 * Enhanced UI message with display properties
 */
export type UIChatMessage = ChatMessage & {
  id: string
  timestamp: Date
  isStreaming?: boolean
  thinkingTime?: number
  isCollapsed?: boolean
  stepCount?: number
  steps?: ExecutionStep[]
  attachments?: Attachment[]
}

/**
 * Cached message format for in-memory storage
 */
export type CachedMessage = {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  createdAt: Date | string
  stepCount?: number
  steps?: ExecutionStep[]
  attachments?: Attachment[]
  isStreaming?: boolean
}

// ─────────────────────────────────────────────
// Execution Step Types
// ─────────────────────────────────────────────

/**
 * Execution step data for AI processing trace
 */
export type ExecutionStep = {
  id?: string
  stepOrder: number
  type: AiStepType
  metadata: Record<string, any>
  parallelKey?: string
  parentStepId?: string
  createdAt?: Date
}

/**
 * Raw step data for collection during processing
 */
export type ExecutionStepData = {
  stepOrder: number
  type: AiStepType
  metadata: Record<string, any>
  parallelKey?: string
  parentStepId?: string
}

// ─────────────────────────────────────────────
// Tool Types
// ─────────────────────────────────────────────

/**
 * Tool call representation
 */
export type ToolCall = {
  id: string
  type: 'function' | 'search' | 'calculation'
  name: string
  parameters: Record<string, any>
  result?: any
}

// ─────────────────────────────────────────────
// Attachment Types
// ─────────────────────────────────────────────

/**
 * Attachment data for file uploads
 */
export type Attachment = {
  id?: string
  fileName: string
  fileType: string
  fileSize: number
  url: string
}

/**
 * Raw attachment data for persistence
 */
export type AttachmentData = Attachment

// ─────────────────────────────────────────────
// Conversation Types
// ─────────────────────────────────────────────

/**
 * Basic conversation metadata
 */
export type Conversation = {
  id: string
  title: string | null
  contextEntityType: string | null
  contextEntityId: string | null
  createdAt: Date | string
  userId: string
  updatedAt?: Date
}

/**
 * Cached conversation format
 */
export type CachedConversation = Conversation

/**
 * Conversation with full message data
 */
export type ConversationWithMessages = {
  id: string
  userId: string
  title: string | null
  contextEntityType: string | null
  contextEntityId: string | null
  createdAt: Date
  updatedAt: Date
  messages: Array<{
    id: string
    role: AiMessageRole
    content: string
    createdAt: Date
    steps: ExecutionStep[]
    attachments: Attachment[]
  }>
}

// ─────────────────────────────────────────────
// Request/Response Types
// ─────────────────────────────────────────────

/**
 * AI Pane Chat API request
 */
export type AIPaneChatRequest = {
  messages: {
    role: 'user' | 'assistant' | 'system'
    content: MessageContent
  }[]
  model?: string
  context?: string
  settings?: Record<string, any>
  conversationId?: string
  contextEntityType?: string
  contextEntityId?: string
}

/**
 * Message data for persistence operations
 */
export type MessageData = {
  role: AiMessageRole
  content: string
  steps?: ExecutionStepData[]
  attachments?: AttachmentData[]
}

/**
 * Complete conversation turn for atomic persistence
 */
export type ConversationTurnData = {
  conversationId: string
  userMessage: MessageData
  assistantMessage: MessageData
}

/**
 * Conversation creation data
 */
export type ConversationCreateData = {
  userId: string
  title?: string
  contextEntityType?: string
  contextEntityId?: string
}

// ─────────────────────────────────────────────
// Result Types
// ─────────────────────────────────────────────

/**
 * Service operation result
 */
export type ServiceResult<T> = {
  success: boolean
  data?: T
  error?: string
}

/**
 * Enhanced error information
 */
export type ServiceError = {
  message: string
  code?: string
  details?: Record<string, unknown>
  timestamp: Date
}

/**
 * Enhanced result with structured error handling
 */
export type EnhancedServiceResult<T> = {
  success: boolean
  data?: T
  error?: ServiceError
}

// ─────────────────────────────────────────────
// Type Guards
// ─────────────────────────────────────────────

/**
 * Type guard for string message content
 */
export function isStringContent(content: MessageContent): content is string {
  return typeof content === 'string'
}

/**
 * Type guard for multimodal message content
 */
export function isMultimodalContent(
  content: MessageContent
): content is { type: 'text'; text: string }[] {
  return Array.isArray(content)
}

/**
 * Extract text from message content regardless of format
 */
export function extractTextFromContent(content: MessageContent): string {
  if (isStringContent(content)) {
    return content
  }
  return content.map(part => part.text).join('')
}
