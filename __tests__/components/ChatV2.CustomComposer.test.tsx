import React from 'react'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'

import { _Test_CustomComposer as CustomComposer } from '@/app/(conv)/aipane/components/ChatV2'
import { CONTEXT_CONFIG } from '@/app/api/aipane/assistant/config'

// Mock lowlight and code-block-lowlight as minimal stubs to avoid heavy imports
jest.mock('lowlight', () => ({ lowlight: { registerLanguage: jest.fn() } }))
jest.mock('@tiptap/extension-code-block-lowlight', () => ({
  __esModule: true,
  default: { configure: jest.fn(() => ({})) },
}))

// Mock ReadOnlyTiptapEditor to avoid loading tiptap in this unit test
jest.mock('@/app/components/editor/ReadOnlyTiptapEditor', () => ({
  __esModule: true,
  default: () => <div data-testid="ro-editor" />,
}))

// Mock assistant-ui dependencies
jest.mock('@assistant-ui/react', () => {
  const React = require('react')
  return {
    ComposerPrimitive: {
      Root: ({ children, ...props }: any) => (
        <div data-testid="composer-root" {...props}>
          {children}
        </div>
      ),
      Input: ({ ...props }: any) => (
        <textarea data-testid="composer-input" {...props} />
      ),
      Send: React.forwardRef((props: any, ref: any) => (
        <button data-testid="send-button" ref={ref} {...props}>
          Send
        </button>
      )),
    },
  }
})

// Mock react-ai-sdk module to avoid ESM import issues in tests
jest.mock('@assistant-ui/react-ai-sdk', () => ({
  useChatRuntime: jest.fn(),
}))

// silence react-hot-toast imports (not used here but part of component tree)
jest.mock('react-hot-toast', () => ({ success: jest.fn(), error: jest.fn() }))

describe('CustomComposer', () => {
  it('disables send button when input is empty', () => {
    render(<CustomComposer inputValue="" setInputValue={() => {}} />)
    const sendBtn = screen.getByTestId('send-button')
    expect(sendBtn).toBeDisabled()
  })

  it('enables send button with valid input', () => {
    render(<CustomComposer inputValue="Hello" setInputValue={() => {}} />)
    const sendBtn = screen.getByTestId('send-button')
    expect(sendBtn).not.toBeDisabled()
  })

  it('disables send button and shows error when input exceeds limit', () => {
    const longText = 'x'.repeat(CONTEXT_CONFIG.MAX_INPUT_CHARS + 5)
    render(<CustomComposer inputValue={longText} setInputValue={() => {}} />)

    const sendBtn = screen.getByTestId('send-button')
    expect(sendBtn).toBeDisabled()

    // Error message should appear
    const errorMsg = screen.getByText(/message is too long/i)
    expect(errorMsg).toBeInTheDocument()
  })
})
