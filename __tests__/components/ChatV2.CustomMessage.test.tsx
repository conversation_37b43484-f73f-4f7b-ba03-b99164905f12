import React from 'react'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'

// Underscore-prefixed exports are exposed only for testing purposes
import { _Test_CustomMessage as CustomMessage } from '@/app/(conv)/aipane/components/ChatV2'

// Keep a mutable message object that our mock implementation can read
let mockMessage: any = {}

// Mock assistant-ui dependencies used by ChatV2 / CustomMessage
jest.mock('@assistant-ui/react-ai-sdk', () => ({
  useChatRuntime: jest.fn(),
}))

jest.mock('@assistant-ui/react', () => {
  const React = require('react')
  return {
    // The selector pattern used by useMessage: selector(message)
    useMessage: (selector: any) => selector(mockMessage),
    // Minimal stubs for primitives utilised in CustomMessage
    MessagePrimitive: {
      Root: ({ children }: any) => <div data-testid="msg-root">{children}</div>,
      Parts: () => <div data-testid="msg-parts" />, // not used in these tests
      Content: () => <div data-testid="msg-content" />, // not used in these tests
    },
    AssistantRuntimeProvider: ({ children }: any) => <>{children}</>,
    ThreadPrimitive: {
      Root: ({ children }: any) => <>{children}</>,
      Messages: () => null,
    },
    ComposerPrimitive: {
      Root: ({ children }: any) => <>{children}</>,
    },
  }
})

// Mock toast to silence warnings
jest.mock('react-hot-toast', () => ({ success: jest.fn(), error: jest.fn() }))

// Mock ReadOnlyTiptapEditor so we can inspect content prop
jest.mock('@/app/components/editor/ReadOnlyTiptapEditor', () => ({
  __esModule: true,
  default: ({ content }: any) => (
    <div data-testid="ro-editor" data-content={content as string} />
  ),
}))

describe('CustomMessage content handling', () => {
  afterEach(() => {
    mockMessage = {}
    jest.clearAllMocks()
  })

  it('renders a Tiptap JSON string correctly', () => {
    const jsonString = JSON.stringify({ type: 'doc', content: [] })
    mockMessage = {
      content: jsonString,
      role: 'assistant',
      isStreaming: false,
    }

    render(<CustomMessage />)
    const editor = screen.getByTestId('ro-editor')
    expect(editor).toHaveAttribute('data-content', jsonString)
  })

  it('renders a markdown string correctly', () => {
    const md = '# Hello World'
    mockMessage = { content: md, role: 'assistant', isStreaming: false }

    render(<CustomMessage />)
    const editor = screen.getByTestId('ro-editor')
    expect(editor).toHaveAttribute('data-content', md)
  })

  it('flattens a multi-part text array (streaming case)', () => {
    const parts = [
      { type: 'text', text: 'Hello ' },
      { type: 'text', text: 'World' },
    ]
    mockMessage = { content: parts, role: 'assistant', isStreaming: false }

    render(<CustomMessage />)
    const editor = screen.getByTestId('ro-editor')
    expect(editor).toHaveAttribute('data-content', 'Hello World')
  })

  it('handles null / undefined content gracefully', () => {
    mockMessage = { content: null, role: 'assistant', isStreaming: false }
    expect(() => render(<CustomMessage />)).not.toThrow()
  })
})
