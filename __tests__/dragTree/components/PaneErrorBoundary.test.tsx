/**
 * Unit tests for PaneErrorBoundary component
 */

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import PaneErrorBoundary, {
  PaneType,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/PaneErrorBoundary'

// Mock UI components
jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  ),
}))

// Component that throws an error for testing
const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test pane error message')
  }
  return <div data-testid="working-pane">Pane is working</div>
}

describe('PaneErrorBoundary', () => {
  // Suppress console.error for these tests since we're intentionally throwing errors
  const originalError = console.error
  beforeAll(() => {
    console.error = jest.fn()
  })
  afterAll(() => {
    console.error = originalError
  })

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Normal Operation', () => {
    it('should render children when no error occurs', () => {
      render(
        <PaneErrorBoundary paneType="outline">
          <ThrowError shouldThrow={false} />
        </PaneErrorBoundary>
      )

      expect(screen.getByTestId('working-pane')).toBeInTheDocument()
      expect(screen.getByText('Pane is working')).toBeInTheDocument()
    })
  })

  describe('Error Handling - Outline Pane', () => {
    it('should catch errors and display outline-specific fallback UI', () => {
      render(
        <PaneErrorBoundary paneType="outline">
          <ThrowError shouldThrow={true} />
        </PaneErrorBoundary>
      )

      expect(
        screen.getByText('Tree outline temporarily unavailable')
      ).toBeInTheDocument()
      expect(
        screen.getByText(/The hierarchical tree view encountered an error/)
      ).toBeInTheDocument()
      expect(screen.getByText('Retry Tree')).toBeInTheDocument()
    })

    it('should log errors with outline pane context', () => {
      render(
        <PaneErrorBoundary paneType="outline">
          <ThrowError shouldThrow={true} />
        </PaneErrorBoundary>
      )

      expect(console.error).toHaveBeenCalledWith(
        '[Outline Pane] Error boundary caught error:',
        expect.any(Error),
        expect.any(Object)
      )
    })
  })

  describe('Error Handling - Visualization Pane', () => {
    it('should catch errors and display visualization-specific fallback UI', () => {
      render(
        <PaneErrorBoundary paneType="visualization">
          <ThrowError shouldThrow={true} />
        </PaneErrorBoundary>
      )

      expect(
        screen.getByText('Flow diagram temporarily unavailable')
      ).toBeInTheDocument()
      expect(
        screen.getByText(/The visual flow diagram encountered an error/)
      ).toBeInTheDocument()
      expect(screen.getByText('Retry Diagram')).toBeInTheDocument()
    })

    it('should log errors with visualization pane context', () => {
      render(
        <PaneErrorBoundary paneType="visualization">
          <ThrowError shouldThrow={true} />
        </PaneErrorBoundary>
      )

      expect(console.error).toHaveBeenCalledWith(
        '[Visualization Pane] Error boundary caught error:',
        expect.any(Error),
        expect.any(Object)
      )
    })
  })

  describe('Custom Fallback', () => {
    it('should display custom fallback when provided', () => {
      const customFallback = (
        <div data-testid="custom-pane-fallback">Custom pane error message</div>
      )

      render(
        <PaneErrorBoundary paneType="outline" fallback={customFallback}>
          <ThrowError shouldThrow={true} />
        </PaneErrorBoundary>
      )

      expect(screen.getByTestId('custom-pane-fallback')).toBeInTheDocument()
      expect(screen.getByText('Custom pane error message')).toBeInTheDocument()
      expect(
        screen.queryByText('Tree outline temporarily unavailable')
      ).not.toBeInTheDocument()
    })
  })

  describe('Retry Functionality', () => {
    it('should call onRetry callback when retry button is clicked', () => {
      const onRetryMock = jest.fn()

      render(
        <PaneErrorBoundary paneType="outline" onRetry={onRetryMock}>
          <ThrowError shouldThrow={true} />
        </PaneErrorBoundary>
      )

      const retryButton = screen.getByText('Retry Tree')
      fireEvent.click(retryButton)

      expect(onRetryMock).toHaveBeenCalledTimes(1)
    })

    it('should reset error state when retry is clicked', () => {
      let shouldThrow = true
      const TestComponent = () => <ThrowError shouldThrow={shouldThrow} />

      const { rerender } = render(
        <PaneErrorBoundary paneType="visualization">
          <TestComponent />
        </PaneErrorBoundary>
      )

      // Error should be displayed
      expect(
        screen.getByText('Flow diagram temporarily unavailable')
      ).toBeInTheDocument()

      // Fix the error condition
      shouldThrow = false

      // Click retry
      const retryButton = screen.getByText('Retry Diagram')
      fireEvent.click(retryButton)

      // Re-render with fixed component
      rerender(
        <PaneErrorBoundary paneType="visualization">
          <ThrowError shouldThrow={false} />
        </PaneErrorBoundary>
      )

      // Should show working component
      expect(screen.getByTestId('working-pane')).toBeInTheDocument()
      expect(
        screen.queryByText('Flow diagram temporarily unavailable')
      ).not.toBeInTheDocument()
    })
  })

  describe('Development Mode', () => {
    const originalEnv = process.env.NODE_ENV

    afterEach(() => {
      process.env.NODE_ENV = originalEnv
    })

    it('should show error details in development mode', () => {
      process.env.NODE_ENV = 'development'

      render(
        <PaneErrorBoundary paneType="outline">
          <ThrowError shouldThrow={true} />
        </PaneErrorBoundary>
      )

      expect(screen.getByText('Show Error Details')).toBeInTheDocument()
    })

    it('should not show error details in production mode', () => {
      process.env.NODE_ENV = 'production'

      render(
        <PaneErrorBoundary paneType="outline">
          <ThrowError shouldThrow={true} />
        </PaneErrorBoundary>
      )

      expect(screen.queryByText('Show Error Details')).not.toBeInTheDocument()
    })
  })

  describe('Pane Type Configurations', () => {
    const paneTypes: PaneType[] = ['outline', 'visualization']

    paneTypes.forEach(paneType => {
      it(`should render appropriate UI for ${paneType} pane type`, () => {
        render(
          <PaneErrorBoundary paneType={paneType}>
            <ThrowError shouldThrow={true} />
          </PaneErrorBoundary>
        )

        // Should have appropriate retry button text
        const expectedRetryText =
          paneType === 'outline' ? 'Retry Tree' : 'Retry Diagram'
        expect(screen.getByText(expectedRetryText)).toBeInTheDocument()

        // Should have appropriate title
        const expectedTitle =
          paneType === 'outline'
            ? 'Tree outline temporarily unavailable'
            : 'Flow diagram temporarily unavailable'
        expect(screen.getByText(expectedTitle)).toBeInTheDocument()
      })
    })
  })
})
