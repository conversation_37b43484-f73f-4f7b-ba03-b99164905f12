/**
 * Unit tests for ChatErrorBoundary component
 */

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import ChatErrorBoundary from '@/app/(conv)/dragTree/[dragTreeId]/components/chat/ChatErrorBoundary'

// Mock UI components
jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  ),
}))

// Component that throws an error for testing
const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error message')
  }
  return <div data-testid="working-component">Component is working</div>
}

describe('ChatErrorBoundary', () => {
  // Suppress console.error for these tests since we're intentionally throwing errors
  const originalError = console.error
  beforeAll(() => {
    console.error = jest.fn()
  })
  afterAll(() => {
    console.error = originalError
  })

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Normal Operation', () => {
    it('should render children when no error occurs', () => {
      render(
        <ChatErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ChatErrorBoundary>
      )

      expect(screen.getByTestId('working-component')).toBeInTheDocument()
      expect(screen.getByText('Component is working')).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('should catch errors and display fallback UI', () => {
      render(
        <ChatErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ChatErrorBoundary>
      )

      expect(
        screen.getByText('Chat temporarily unavailable')
      ).toBeInTheDocument()
      expect(
        screen.getByText(/Something went wrong with the chat interface/)
      ).toBeInTheDocument()
      expect(screen.getByText('Try Again')).toBeInTheDocument()
    })

    it('should display custom fallback when provided', () => {
      const customFallback = (
        <div data-testid="custom-fallback">Custom error message</div>
      )

      render(
        <ChatErrorBoundary fallback={customFallback}>
          <ThrowError shouldThrow={true} />
        </ChatErrorBoundary>
      )

      expect(screen.getByTestId('custom-fallback')).toBeInTheDocument()
      expect(screen.getByText('Custom error message')).toBeInTheDocument()
      expect(
        screen.queryByText('Chat temporarily unavailable')
      ).not.toBeInTheDocument()
    })

    it('should call onRetry callback when retry button is clicked', () => {
      const onRetryMock = jest.fn()

      render(
        <ChatErrorBoundary onRetry={onRetryMock}>
          <ThrowError shouldThrow={true} />
        </ChatErrorBoundary>
      )

      const retryButton = screen.getByText('Try Again')
      fireEvent.click(retryButton)

      expect(onRetryMock).toHaveBeenCalledTimes(1)
    })

    it('should reset error state when retry is clicked', () => {
      let shouldThrow = true
      const TestComponent = () => <ThrowError shouldThrow={shouldThrow} />

      const { rerender } = render(
        <ChatErrorBoundary>
          <TestComponent />
        </ChatErrorBoundary>
      )

      // Error should be displayed
      expect(
        screen.getByText('Chat temporarily unavailable')
      ).toBeInTheDocument()

      // Fix the error condition
      shouldThrow = false

      // Click retry
      const retryButton = screen.getByText('Try Again')
      fireEvent.click(retryButton)

      // Re-render with fixed component
      rerender(
        <ChatErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ChatErrorBoundary>
      )

      // Should show working component
      expect(screen.getByTestId('working-component')).toBeInTheDocument()
      expect(
        screen.queryByText('Chat temporarily unavailable')
      ).not.toBeInTheDocument()
    })

    it('should log errors to console', () => {
      render(
        <ChatErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ChatErrorBoundary>
      )

      expect(console.error).toHaveBeenCalledWith(
        '[Chat] Error boundary caught error:',
        expect.any(Error),
        expect.any(Object)
      )
    })
  })

  describe('Development Mode', () => {
    const originalEnv = process.env.NODE_ENV

    afterEach(() => {
      process.env.NODE_ENV = originalEnv
    })

    it('should show error details in development mode', () => {
      process.env.NODE_ENV = 'development'

      render(
        <ChatErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ChatErrorBoundary>
      )

      expect(screen.getByText('Show Error Details')).toBeInTheDocument()
    })

    it('should not show error details in production mode', () => {
      process.env.NODE_ENV = 'production'

      render(
        <ChatErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ChatErrorBoundary>
      )

      expect(screen.queryByText('Show Error Details')).not.toBeInTheDocument()
    })
  })
})
