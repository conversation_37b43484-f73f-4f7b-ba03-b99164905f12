/**
 * Unit tests for Assistant<PERSON><PERSON><PERSON><PERSON> component - focusing on assistant-ui integration
 */

import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import Assistant<PERSON>Wrapper from '@/app/(conv)/dragTree/[dragTreeId]/components/chat/AssistantUIWrapper'

// Mock assistant-ui dependencies
jest.mock('@assistant-ui/react-ai-sdk', () => ({
  useChatRuntime: jest.fn(() => ({
    messages: [],
    isRunning: false,
    append: jest.fn(),
  })),
}))

jest.mock('@assistant-ui/react', () => ({
  AssistantRuntimeProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="assistant-runtime-provider">{children}</div>
  ),
  MessagePrimitive: {
    Root: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="message-root">{children}</div>
    ),
    Content: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="message-content">{children}</div>
    ),
    Parts: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="message-parts">{children}</div>
    ),
  },
  ThreadPrimitive: {
    Root: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="thread-root">{children}</div>
    ),
    Messages: ({ components }: { components: any }) => (
      <div data-testid="thread-messages">
        {components?.Message && <components.Message />}
      </div>
    ),
  },
  ComposerPrimitive: {
    Root: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="composer-root">{children}</div>
    ),
    Input: React.forwardRef<HTMLTextAreaElement, any>((props, ref) => (
      <textarea ref={ref} data-testid="composer-input" {...props} />
    )),
    Send: React.forwardRef<HTMLButtonElement, any>((props, ref) => (
      <button ref={ref} data-testid="composer-send" {...props} />
    )),
  },
  useMessage: jest.fn(() => ({
    content: 'Test message content',
    role: 'assistant',
    status: { type: 'complete' },
    id: 'msg_test123',
  })),
}))

jest.mock('@/app/components/editor/ReadOnlyTiptapEditor', () => {
  return function MockReadOnlyTiptapEditor() {
    return <div data-testid="readonly-tiptap-editor">TipTap Editor</div>
  }
})

jest.mock('react-hot-toast', () => ({
  __esModule: true,
  default: {
    success: jest.fn(),
    error: jest.fn(),
  },
}))

describe('AssistantUIWrapper Component', () => {
  const defaultProps = {
    conversationId: 'thread_test123',
    initialMessages: [
      {
        id: 'msg_1',
        role: 'user' as const,
        content: 'Hello',
        createdAt: '2024-01-01T00:00:00Z',
      },
      {
        id: 'msg_2',
        role: 'assistant' as const,
        content: 'Hi there!',
        createdAt: '2024-01-01T00:00:00Z',
        stepCount: 2,
        steps: [
          {
            type: 'TOOL_CALL',
            toolName: 'web_search',
            args: { query: 'test' },
          },
        ],
      },
    ],
    onMessageFinish: jest.fn(),
    hasMoreMessages: false,
    isLoadingMoreMessages: false,
    onLoadMoreMessages: jest.fn(),
    liveSteps: [],
    isStreamingSteps: false,
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Basic Rendering', () => {
    it('should render assistant-ui components', () => {
      render(<AssistantUIWrapper {...defaultProps} />)

      expect(
        screen.getByTestId('assistant-runtime-provider')
      ).toBeInTheDocument()
      expect(screen.getByTestId('thread-root')).toBeInTheDocument()
      expect(screen.getByTestId('thread-messages')).toBeInTheDocument()
      expect(screen.getByTestId('composer-root')).toBeInTheDocument()
      expect(screen.getByTestId('composer-input')).toBeInTheDocument()
      expect(screen.getByTestId('composer-send')).toBeInTheDocument()
    })

    it('should initialize chat runtime with correct props', () => {
      const { useChatRuntime } = require('@assistant-ui/react-ai-sdk')
      const useChatRuntimeSpy = jest.fn(() => ({
        messages: [],
        isRunning: false,
        append: jest.fn(),
      }))
      useChatRuntime.mockImplementation(useChatRuntimeSpy)

      render(<AssistantUIWrapper {...defaultProps} />)

      expect(useChatRuntimeSpy).toHaveBeenCalledWith({
        api: '/api/aipane/chat',
        initialMessages: expect.any(Array),
        body: {
          conversationId: 'thread_test123',
        },
        onFinish: defaultProps.onMessageFinish,
      })
    })
  })

  describe('Message Formatting', () => {
    it('should format initial messages correctly', () => {
      const { useChatRuntime } = require('@assistant-ui/react-ai-sdk')
      const useChatRuntimeSpy = jest.fn(() => ({
        messages: [],
        isRunning: false,
        append: jest.fn(),
      }))
      useChatRuntime.mockImplementation(useChatRuntimeSpy)

      render(<AssistantUIWrapper {...defaultProps} />)

      const callArgs = useChatRuntimeSpy.mock.calls[0][0]
      const formattedMessages = callArgs.initialMessages

      expect(formattedMessages).toBeDefined()
      expect(Array.isArray(formattedMessages)).toBe(true)
      expect(formattedMessages.length).toBeGreaterThan(0)

      // Check that messages have required properties
      if (formattedMessages.length > 0) {
        expect(formattedMessages[0]).toHaveProperty('id')
        expect(formattedMessages[0]).toHaveProperty('role')
        expect(formattedMessages[0]).toHaveProperty('content')
      }
    })

    it('should filter out system messages', () => {
      const { useChatRuntime } = require('@assistant-ui/react-ai-sdk')
      const useChatRuntimeSpy = jest.fn(() => ({
        messages: [],
        isRunning: false,
        append: jest.fn(),
      }))
      useChatRuntime.mockImplementation(useChatRuntimeSpy)

      const propsWithSystemMessage = {
        ...defaultProps,
        initialMessages: [
          ...defaultProps.initialMessages,
          {
            id: 'msg_3',
            role: 'system' as const,
            content: 'System message',
            createdAt: '2024-01-01T00:00:00Z',
          },
        ],
      }

      render(<AssistantUIWrapper {...propsWithSystemMessage} />)

      const callArgs = useChatRuntimeSpy.mock.calls[0][0]
      const formattedMessages = callArgs.initialMessages

      // Check that system messages are handled appropriately
      // (The actual filtering logic may vary in implementation)
      expect(formattedMessages).toBeDefined()
      expect(Array.isArray(formattedMessages)).toBe(true)
    })
  })

  describe('Input Validation', () => {
    it('should render input and send button', () => {
      render(<AssistantUIWrapper {...defaultProps} />)

      const input = screen.getByTestId('composer-input')
      const sendButton = screen.getByTestId('composer-send')

      expect(input).toBeInTheDocument()
      expect(sendButton).toBeInTheDocument()
    })
  })

  describe('Live Steps Integration', () => {
    it('should display live steps when streaming', () => {
      const propsWithLiveSteps = {
        ...defaultProps,
        liveSteps: [
          {
            type: 'TOOL_CALL',
            toolName: 'web_search',
            args: { query: 'live search' },
          },
        ],
        isStreamingSteps: true,
      }

      render(<AssistantUIWrapper {...propsWithLiveSteps} />)

      // Component should render with live steps
      expect(screen.getByTestId('thread-messages')).toBeInTheDocument()
    })

    it('should use stored steps when not streaming', () => {
      const propsWithStoredSteps = {
        ...defaultProps,
        liveSteps: [],
        isStreamingSteps: false,
      }

      render(<AssistantUIWrapper {...propsWithStoredSteps} />)

      // Should use steps from initialMessages instead of liveSteps
      expect(screen.getByTestId('thread-messages')).toBeInTheDocument()
    })
  })

  describe('Scroll Management', () => {
    it('should provide scroll to bottom functionality', () => {
      render(<AssistantUIWrapper {...defaultProps} />)

      // Should render scroll container
      expect(screen.getByTestId('thread-root')).toBeInTheDocument()
    })
  })

  describe('Copy Functionality', () => {
    it('should handle copy operations', async () => {
      render(<AssistantUIWrapper {...defaultProps} />)

      // Component should render without errors
      expect(screen.getByTestId('thread-messages')).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('should handle missing conversationId gracefully', () => {
      const propsWithoutConversationId = {
        ...defaultProps,
        conversationId: undefined,
      }

      expect(() => {
        render(<AssistantUIWrapper {...propsWithoutConversationId} />)
      }).not.toThrow()
    })

    it('should handle empty initial messages', () => {
      const propsWithEmptyMessages = {
        ...defaultProps,
        initialMessages: [],
      }

      expect(() => {
        render(<AssistantUIWrapper {...propsWithEmptyMessages} />)
      }).not.toThrow()
    })
  })

  describe('Tool Call Rendering', () => {
    it('should render web search tool calls', () => {
      const { useMessage } = require('@assistant-ui/react')

      useMessage.mockReturnValue({
        content: '',
        role: 'assistant',
        status: { type: 'running' },
        id: 'msg_test123',
      })

      render(<AssistantUIWrapper {...defaultProps} />)

      // Should render message components
      expect(screen.getByTestId('thread-messages')).toBeInTheDocument()
    })
  })
})
