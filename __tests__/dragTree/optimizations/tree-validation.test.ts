/**
 * Tests for Tree Validation and Error Recovery
 *
 * Validates the reliability improvements implemented in Phase 2:
 * - Comprehensive tree structure validation
 * - Error detection and recovery mechanisms
 * - Data integrity checks
 */

import {
  validateTreeStructure,
  quickValidateTree,
  sanitizeTreeStructure,
} from '@/app/stores/dragtree_store/utils/tree-validation'
import { TreeNode } from '@/app/types'

describe('Tree Validation System', () => {
  const validTree: TreeNode = {
    id: 'root',
    label: 'Root Node',
    type: 'category',
    children: [
      {
        id: 'child1',
        label: 'Child 1',
        type: 'question',
        children: [],
        isInterestedIn: false,
      },
      {
        id: 'child2',
        label: 'Child 2',
        type: 'category',
        children: [
          {
            id: 'grandchild1',
            label: 'Grandchild 1',
            type: 'question',
            children: [],
            isInterestedIn: true,
          },
        ],
        isInterestedIn: false,
      },
    ],
    isInterestedIn: false,
  }

  describe('validateTreeStructure', () => {
    it('should validate a correct tree structure', () => {
      const result = validateTreeStructure(validTree)

      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.stats.totalNodes).toBe(4)
      expect(result.stats.maxDepth).toBe(2)
      expect(result.stats.duplicateIds).toBe(0)
      expect(result.stats.validationTime).toBeGreaterThan(0)
    })

    it('should detect duplicate node IDs', () => {
      const treeWithDuplicates: TreeNode = {
        id: 'root',
        label: 'Root',
        type: 'category',
        children: [
          {
            id: 'duplicate',
            label: 'First',
            type: 'question',
            children: [],
            isInterestedIn: false,
          },
          {
            id: 'duplicate', // Duplicate ID
            label: 'Second',
            type: 'question',
            children: [],
            isInterestedIn: false,
          },
        ],
        isInterestedIn: false,
      }

      const result = validateTreeStructure(treeWithDuplicates)

      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].type).toBe('DUPLICATE_ID')
      expect(result.errors[0].nodeId).toBe('duplicate')
      expect(result.stats.duplicateIds).toBe(1)
    })

    it('should detect missing required fields', () => {
      const invalidTree = {
        id: 'root',
        // Missing label
        type: 'category',
        children: [
          {
            // Missing id
            label: 'Child',
            type: 'question',
            children: [],
            isInterestedIn: false,
          },
        ],
        isInterestedIn: false,
      } as TreeNode

      const result = validateTreeStructure(invalidTree)

      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
      expect(result.errors.some(e => e.type === 'MISSING_REQUIRED_FIELD')).toBe(
        true
      )
    })

    it('should detect invalid node types', () => {
      const treeWithInvalidType: TreeNode = {
        id: 'root',
        label: 'Root',
        type: 'invalid-type' as any,
        children: [],
        isInterestedIn: false,
      }

      const result = validateTreeStructure(treeWithInvalidType)

      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.type === 'INVALID_TYPE')).toBe(true)
    })

    it('should warn about deep nesting', () => {
      // Create a deeply nested tree (depth > 10)
      let deepTree: TreeNode = {
        id: 'root',
        label: 'Root',
        type: 'category',
        children: [],
        isInterestedIn: false,
      }

      let current = deepTree
      for (let i = 1; i <= 12; i++) {
        const child: TreeNode = {
          id: `node_${i}`,
          label: `Node ${i}`,
          type: 'category',
          children: [],
          isInterestedIn: false,
        }
        current.children.push(child)
        current = child
      }

      const result = validateTreeStructure(deepTree)

      expect(result.warnings.some(w => w.type === 'DEEP_NESTING')).toBe(true)
    })

    it('should warn about nodes with many children', () => {
      const treeWithManyChildren: TreeNode = {
        id: 'root',
        label: 'Root',
        type: 'category',
        children: Array.from({ length: 60 }, (_, i) => ({
          id: `child_${i}`,
          label: `Child ${i}`,
          type: 'question' as const,
          children: [],
          isInterestedIn: false,
        })),
        isInterestedIn: false,
      }

      const result = validateTreeStructure(treeWithManyChildren)

      expect(result.warnings.some(w => w.type === 'PERFORMANCE_CONCERN')).toBe(
        true
      )
    })

    it('should handle null tree', () => {
      const result = validateTreeStructure(null)

      expect(result.isValid).toBe(false)
      expect(result.errors[0].type).toBe('INVALID_STRUCTURE')
      expect(result.stats.totalNodes).toBe(0)
    })

    it('should detect potential circular references via duplicate IDs', () => {
      // Create a tree that simulates circular reference by having same ID appear multiple times
      const treeWithCircularIds: TreeNode = {
        id: 'root',
        label: 'Root',
        type: 'category',
        children: [
          {
            id: 'child1',
            label: 'Child 1',
            type: 'category',
            children: [
              {
                id: 'root', // Same ID as root - simulates circular reference
                label: 'Circular Node',
                type: 'category',
                children: [],
                isInterestedIn: false,
              },
            ],
            isInterestedIn: false,
          },
        ],
        isInterestedIn: false,
      }

      const result = validateTreeStructure(treeWithCircularIds)

      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.type === 'DUPLICATE_ID')).toBe(true)
    })
  })

  describe('quickValidateTree', () => {
    it('should quickly validate a correct tree', () => {
      const result = quickValidateTree(validTree)
      expect(result).toBe(true)
    })

    it('should quickly detect invalid tree', () => {
      const invalidTree = {
        id: 'root',
        // Missing required fields
        children: [],
      } as TreeNode

      const result = quickValidateTree(invalidTree)
      expect(result).toBe(false)
    })

    it('should handle null tree', () => {
      const result = quickValidateTree(null)
      expect(result).toBe(false)
    })
  })

  describe('sanitizeTreeStructure', () => {
    it('should sanitize a tree with issues', () => {
      const problematicTree = {
        id: 'root',
        label: '  Root Node  ', // Extra whitespace
        type: 'category',
        children: [
          {
            id: 'child1',
            label: '', // Empty label
            type: 'question',
            children: [],
            isInterestedIn: false,
          },
          {
            id: 'child2',
            label: 'Valid Child',
            type: 'invalid-type', // Invalid type
            children: [],
            isInterestedIn: false,
          },
          null, // Null child
          {
            id: 'child3',
            label: 'Another Child',
            type: 'category',
            children: [],
            isInterestedIn: false,
          },
        ],
        isInterestedIn: false,
      } as any

      const result = sanitizeTreeStructure(problematicTree)

      expect(result).toBeDefined()
      expect(result!.label).toBe('Root Node') // Trimmed
      expect(result!.children).toHaveLength(2) // Null child and invalid child removed

      // Check remaining valid children
      const validChildren = result!.children
      expect(validChildren.some(c => c.label === 'Another Child')).toBe(true)
    })

    it('should remove duplicate IDs', () => {
      const treeWithDuplicates = {
        id: 'root',
        label: 'Root',
        type: 'category',
        children: [
          {
            id: 'duplicate',
            label: 'First',
            type: 'question',
            children: [],
            isInterestedIn: false,
          },
          {
            id: 'duplicate', // Duplicate ID - should be removed
            label: 'Second',
            type: 'question',
            children: [],
            isInterestedIn: false,
          },
          {
            id: 'unique',
            label: 'Unique',
            type: 'question',
            children: [],
            isInterestedIn: false,
          },
        ],
        isInterestedIn: false,
      } as TreeNode

      const result = sanitizeTreeStructure(treeWithDuplicates)

      expect(result).toBeDefined()
      expect(result!.children).toHaveLength(2) // Duplicate removed
      expect(result!.children.map(c => c.id)).toEqual(['duplicate', 'unique'])
    })

    it('should handle null tree', () => {
      const result = sanitizeTreeStructure(null)
      expect(result).toBeNull()
    })

    it('should handle completely invalid tree', () => {
      const invalidTree = {
        // Missing all required fields
        someRandomProperty: 'value',
      } as any

      const result = sanitizeTreeStructure(invalidTree)
      expect(result).toBeNull()
    })
  })

  describe('Performance', () => {
    it('should validate large trees efficiently', () => {
      // Create a large tree (1000 nodes)
      const largeTree: TreeNode = {
        id: 'root',
        label: 'Root',
        type: 'category',
        children: Array.from({ length: 100 }, (_, i) => ({
          id: `parent_${i}`,
          label: `Parent ${i}`,
          type: 'category' as const,
          children: Array.from({ length: 10 }, (_, j) => ({
            id: `child_${i}_${j}`,
            label: `Child ${i}-${j}`,
            type: 'question' as const,
            children: [],
            isInterestedIn: false,
          })),
          isInterestedIn: false,
        })),
        isInterestedIn: false,
      }

      const startTime = performance.now()
      const result = validateTreeStructure(largeTree)
      const endTime = performance.now()

      expect(result.isValid).toBe(true)
      expect(result.stats.totalNodes).toBe(1101) // 1 root + 100 parents + 1000 children
      expect(endTime - startTime).toBeLessThan(100) // Should complete in under 100ms
      expect(result.warnings.some(w => w.type === 'LARGE_TREE')).toBe(true)
    })
  })
})
