/**
 * Content normalization utilities for AI chat messages
 * Handles conversion between different message content formats
 */

export type MessageContent =
  | string
  | Array<{
      type: string
      text?: string
      image?: string
      [key: string]: any
    }>

/**
 * Normalizes message content to a string format
 * Handles both string content and complex content arrays
 * @param content - The message content to normalize
 * @returns Normalized string content
 */
export function normaliseMessageContent(content: MessageContent): string {
  if (typeof content === 'string') {
    return content
  }

  if (Array.isArray(content)) {
    return content
      .map(part => {
        if (part.type === 'text' && part.text) {
          return part.text
        }
        if (part.type === 'image') {
          return '[Image]'
        }
        return ''
      })
      .filter(Boolean)
      .join(' ')
  }

  return ''
}
