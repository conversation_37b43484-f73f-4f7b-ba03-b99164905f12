export type CachedMessage = {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  createdAt: Date | string
  stepCount?: number
  steps?: any[]
  attachments?: any[]
  isStreaming?: boolean
}

const CACHE_TTL_MS = 60 * 60 * 1000 // 1 hour

export type CachedConversation = {
  id: string
  title: string | null
  contextEntityType: string | null
  contextEntityId: string | null
  createdAt: Date | string
  userId: string
}

const cache = new Map<
  string,
  {
    messages: CachedMessage[]
    conversation?: CachedConversation
    expiresAt: number
  }
>()

const MAX_ENTRIES = 1000

// ─────────────────────────────────────────────
// Internal helpers
// ─────────────────────────────────────────────
/** Remove all expired entries (O(N), called only on writes). */
function pruneExpired() {
  const now = Date.now()
  cache.forEach((entry, key) => {
    if (now > entry.expiresAt) cache.delete(key)
  })
}

/** Refresh expiry so frequently-accessed items stay warm. */
function touch(entry: { expiresAt: number }) {
  entry.expiresAt = Date.now() + CACHE_TTL_MS
}

// ─────────────────────────────────────────────
// Public API
// ─────────────────────────────────────────────
export function getCachedMessages(
  conversationId: string
): CachedMessage[] | null {
  const entry = cache.get(conversationId)
  if (!entry) return null
  if (Date.now() > entry.expiresAt) {
    cache.delete(conversationId)
    return null
  }
  touch(entry) // sliding TTL
  return entry.messages
}

export function getCachedConversation(
  conversationId: string
): CachedConversation | null {
  const entry = cache.get(conversationId)
  if (!entry) return null
  if (Date.now() > entry.expiresAt) {
    cache.delete(conversationId)
    return null
  }
  touch(entry) // sliding TTL
  return entry.conversation || null
}

export function setCachedMessages(
  conversationId: string,
  messages: CachedMessage[],
  conversation?: CachedConversation
) {
  pruneExpired() // keep memory bounded

  const existingEntry = cache.get(conversationId)
  cache.set(conversationId, {
    messages,
    conversation: conversation || existingEntry?.conversation,
    expiresAt: Date.now() + CACHE_TTL_MS,
  })

  // Simple prune strategy: if we exceed MAX_ENTRIES remove the oldest
  if (cache.size > MAX_ENTRIES) {
    const oldestKey = Array.from(cache.entries()).sort(
      (a, b) => a[1].expiresAt - b[1].expiresAt
    )[0][0]
    cache.delete(oldestKey)
  }
}

export function setCachedConversation(
  conversationId: string,
  conversation: CachedConversation
) {
  pruneExpired() // keep memory bounded

  const existingEntry = cache.get(conversationId)
  cache.set(conversationId, {
    messages: existingEntry?.messages || [],
    conversation,
    expiresAt: Date.now() + CACHE_TTL_MS,
  })
}
