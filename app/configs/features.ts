/**
 * Feature flags configuration
 * Simple boolean flags to control feature rollouts
 */

export const FEATURES = {
  /**
   * Enable Chat V2 - New assistant-ui based chat implementation
   * When true, allows access to the new /api/aipane/assistant endpoint
   * and ChatV2 component
   */
  ENABLE_CHAT_V2: true,
} as const

export type FeatureFlag = keyof typeof FEATURES

/**
 * Check if a feature is enabled
 */
export function isFeatureEnabled(feature: FeatureFlag): boolean {
  return FEATURES[feature]
}
