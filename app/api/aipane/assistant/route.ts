import { streamText, createDataStreamResponse } from 'ai'
import { azure } from '@ai-sdk/azure'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import { revalidatePath } from 'next/cache'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import { AIUsageType } from '@prisma/client'
import {
  createExecutionStepCollector,
  finalizeConversationTurn,
} from '@/app/server-actions/ai-chat'
import {
  buildSearchTools,
  type SearchProgressCallback,
} from '@/app/api/dragtree/shared/search-tools'
import { type SearchMetadata } from '@/app/api/dragtree/shared/brave-search'
import { isRateLimited, getRetryAfterSeconds } from '@/app/libs/rateLimiter'
import { standardErrors } from '../shared/errors'
import { AiChatLogger } from '@/app/server-actions/ai-chat/logging'
import { isFeatureEnabled } from '@/app/configs/features'

// Import our V2 configuration and types
import {
  RATE_LIMIT_CONFIG,
  MODEL_CONFIG,
  CONTEXT_CONFIG,
  RETRY_CONFIG,
  TOOL_NAMES,
  ERROR_MESSAGES,
} from './config'
import type {
  AIPaneChatRequest,
  ConversationMetadata,
  MessageMetadata,
} from './types'
import { buildContextMessagesV2 } from './utils'

export const maxDuration = 60

// Initialize structured logger
const chatLogger = new AiChatLogger('aipane-assistant-route')

export async function POST(req: Request) {
  try {
    // Check if Chat V2 is enabled
    if (!isFeatureEnabled('ENABLE_CHAT_V2')) {
      return standardErrors.notFound('Chat V2 is not enabled')
    }

    // Retrieve the authenticated user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return standardErrors.unauthorized()
    }
    const userId = session.user.id

    // Rate limiting
    const rateLimitKey = `${userId}:aipane:assistant`
    if (isRateLimited(rateLimitKey, RATE_LIMIT_CONFIG.WINDOW_MS)) {
      const retryAfter = getRetryAfterSeconds(
        rateLimitKey,
        RATE_LIMIT_CONFIG.WINDOW_MS
      )
      return standardErrors.rateLimited(
        retryAfter,
        RATE_LIMIT_CONFIG.MAX_REQUESTS.toString()
      )
    }

    // Parse request body
    let requestData: AIPaneChatRequest
    try {
      const rawData = await req.json()
      requestData = rawData as AIPaneChatRequest
    } catch (error) {
      chatLogger.error('Failed to parse JSON', error)
      return standardErrors.invalidJson()
    }

    if (!requestData) {
      return standardErrors.missingBody()
    }

    chatLogger.info('Received chat request', { userId })

    // Extract and validate request data
    const {
      messages,
      conversationId,
      metadata,
      model = MODEL_CONFIG.DEFAULT_MODEL,
      settings = {},
    } = requestData

    // Validate conversation ID
    if (!conversationId || !conversationId.startsWith('thread_')) {
      return standardErrors.invalidIdFormat('conversationId', 'thread_*')
    }

    // Validate messages
    if (!Array.isArray(messages) || messages.length === 0) {
      return standardErrors.invalidMessages(ERROR_MESSAGES.MISSING_MESSAGES)
    }

    // Get the latest user message and validate input length
    const latestUserMessage = messages[messages.length - 1]
    if (latestUserMessage.role === 'user') {
      const content =
        typeof latestUserMessage.content === 'string'
          ? latestUserMessage.content
          : JSON.stringify(latestUserMessage.content)

      if (content.length > CONTEXT_CONFIG.MAX_INPUT_CHARS) {
        return standardErrors.invalidMessages(ERROR_MESSAGES.INPUT_TOO_LONG)
      }
    }

    chatLogger.info('Processing chat request', { userId, conversationId })

    // Build context messages using simplified approach
    const contextResult = await buildContextMessagesV2(
      conversationId,
      latestUserMessage,
      metadata?.contextIds
    )

    if (!contextResult.success) {
      return standardErrors.internalError(
        contextResult.error || 'Failed to build context'
      )
    }

    const {
      messages: modelMessages,
      contextText,
      contextIds,
      truncated,
    } = contextResult.data!

    if (truncated) {
      chatLogger.info('Conversation truncated due to length limits', {
        userId,
        conversationId,
      })
    }

    // Initialize execution step collector and search metadata
    const stepCollector = createExecutionStepCollector()
    const searchMetadata: SearchMetadata[] = []
    let finalResponse = ''

    // Create search progress callback
    const searchProgressCallback: SearchProgressCallback = status => {
      chatLogger.debug('Search progress update', { userId })

      if (status.type === 'searching') {
        stepCollector.addToolCall(TOOL_NAMES.WEB_SEARCH, {
          query: status.query,
        })
      } else if (status.type === 'completed') {
        stepCollector.addToolResult(TOOL_NAMES.WEB_SEARCH, {
          query: status.query,
          resultCount: status.resultCount,
        })
      } else if (status.type === 'error') {
        stepCollector.addToolResult(TOOL_NAMES.WEB_SEARCH, {
          error: status.error,
        })
      }
    }

    // Use createDataStreamResponse for real-time streaming
    return createDataStreamResponse({
      execute: dataStream => {
        // Initialize streaming
        dataStream.writeData({
          type: 'stream-start',
          conversationId,
          timestamp: Date.now(),
        })

        // Use streamText for streaming response
        const result = streamText({
          model: azure(model),
          messages: modelMessages,
          tools: buildSearchTools(searchMetadata, searchProgressCallback),
          maxSteps: MODEL_CONFIG.MAX_STEPS,
          temperature: MODEL_CONFIG.TEMPERATURE,
          maxTokens: MODEL_CONFIG.MAX_TOKENS,

          // Stream tool calls in real-time
          onChunk: async chunk => {
            if (chunk.chunk.type === 'tool-call') {
              stepCollector.addToolCall(chunk.chunk.toolName, chunk.chunk.args)

              dataStream.writeData({
                type: 'execution-step',
                step: {
                  type: 'TOOL_CALL',
                  toolName: chunk.chunk.toolName,
                  args: chunk.chunk.args,
                  toolCallId: chunk.chunk.toolCallId,
                  timestamp: Date.now(),
                },
              })
            }

            if (chunk.chunk.type === 'tool-result') {
              stepCollector.addToolResult(
                chunk.chunk.toolName,
                chunk.chunk.result
              )

              dataStream.writeData({
                type: 'execution-step',
                step: {
                  type: 'TOOL_RESULT',
                  toolName: chunk.chunk.toolName,
                  result: chunk.chunk.result,
                  toolCallId: chunk.chunk.toolCallId,
                  timestamp: Date.now(),
                },
              })
            }
          },

          onFinish: async result => {
            try {
              chatLogger.info('Stream finished, starting persistence', {
                userId,
              })

              // Notify client that streaming is complete
              dataStream.writeData({
                type: 'stream-finish',
                timestamp: Date.now(),
              })

              // Extract final response text
              finalResponse = result.text || ''

              // Add reasoning summary if there were steps
              if (stepCollector.getStepCount() > 0) {
                const summary = `Generated response using ${searchMetadata.length} web search(es) and ${stepCollector.getStepCount()} execution step(s)`
                stepCollector.addReasoningSummary(summary)
              }

              // Get latest user message content
              const userMessageContent =
                typeof latestUserMessage.content === 'string'
                  ? latestUserMessage.content
                  : JSON.stringify(latestUserMessage.content)

              // Prepare message metadata
              const messageMetadata: MessageMetadata = {
                runCompleted: true,
                tokenUsage: {
                  promptTokens: result.usage?.promptTokens || 0,
                  completionTokens: result.usage?.completionTokens || 0,
                  totalTokens: result.usage?.totalTokens || 0,
                },
                finishReason: result.finishReason,
                stepCount: stepCollector.getStepCount(),
                toolsUsed:
                  searchMetadata.length > 0 ? [TOOL_NAMES.WEB_SEARCH] : [],
              }

              // Prepare conversation metadata if we have context
              let conversationMetadata: ConversationMetadata | undefined
              if (contextIds && contextIds.length > 0) {
                conversationMetadata = {
                  contextIds,
                  settings,
                }
              }

              // Check if this is the first turn and we need to persist context
              const isFirstTurn = messages.length === 1
              const finalContextText =
                isFirstTurn && contextText && contextText.trim().length > 0
                  ? contextText
                  : undefined

              // Atomically persist all conversation data with retry logic
              const MAX_RETRIES = RETRY_CONFIG.MAX_RETRIES
              let attempt = 0
              let persistResult: Awaited<
                ReturnType<typeof finalizeConversationTurn>
              > | null = null

              while (attempt < MAX_RETRIES) {
                attempt++

                persistResult = await finalizeConversationTurn(
                  conversationId,
                  {
                    role: 'USER',
                    content: userMessageContent,
                  },
                  {
                    role: 'ASSISTANT',
                    content: finalResponse,
                    steps: stepCollector.getSteps(),
                    metadata: messageMetadata,
                  },
                  conversationMetadata,
                  finalContextText
                )

                if (persistResult.success) break

                chatLogger.warn('Persistence attempt failed', {
                  userId,
                  metadata: { attempt },
                })
                await new Promise(res =>
                  setTimeout(res, RETRY_CONFIG.BACKOFF_MS * attempt)
                )
              }

              if (!persistResult?.success) {
                chatLogger.error(
                  'FATAL: Failed to save conversation turn after all retries',
                  { userId }
                )
              } else {
                chatLogger.info('Successfully persisted conversation turn', {
                  userId,
                })

                // Invalidate relevant cache paths to ensure UI gets updated data
                try {
                  // Invalidate the aipane API routes
                  revalidatePath('/api/aipane')
                  // Invalidate conversation-related pages (for any dragTree that might display this conversation)
                  revalidatePath('/dragTree', 'layout')
                  // Invalidate the specific conversation if we had a dragTree context
                  // Note: We don't have dragTreeId here, but layout invalidation should cover it

                  chatLogger.info('Cache invalidation completed', { userId })
                } catch (_cacheErr) {
                  // Don't fail the request if cache invalidation fails
                  chatLogger.warn('Cache invalidation failed', { userId })
                }
              }

              // Log AI usage
              try {
                await createAIUsage({
                  userId,
                  entityType: 'aipane',
                  entityId: conversationId,
                  aiProvider: 'azure_openai',
                  modelName: model,
                  usageType: AIUsageType.CHAT,
                  inputPrompt: userMessageContent,
                  messages: [
                    ...modelMessages,
                    { role: 'assistant', content: finalResponse },
                  ],
                  metadata: {
                    endpoint: '/api/aipane/assistant',
                    promptTokens: result.usage?.promptTokens || 0,
                    completionTokens: result.usage?.completionTokens || 0,
                    totalTokens: result.usage?.totalTokens || 0,
                    finishReason: result.finishReason,
                    hasContext: !!contextText,
                    messageCount: messages.length,
                    conversationId,
                    executionSteps: stepCollector.getStepCount(),
                    searchResults: searchMetadata.length,
                    truncated,
                  },
                  config: settings,
                })
              } catch (error) {
                chatLogger.error('Failed to log AI usage', error, { userId })
              }
            } catch (error) {
              chatLogger.error(
                'FATAL: Failed to process conversation turn',
                error,
                { userId }
              )
            }
          },
        })

        // Merge the streamText result into the data stream
        result.mergeIntoDataStream(dataStream)
      },

      onError: error => {
        chatLogger.error('Stream error occurred', error, { userId })
        // Return a user-friendly error message
        return error instanceof Error
          ? error.message
          : 'An error occurred during streaming'
      },
    })
  } catch (error) {
    chatLogger.error('Chat API error', error)
    return standardErrors.internalError('Error generating response')
  }
}
