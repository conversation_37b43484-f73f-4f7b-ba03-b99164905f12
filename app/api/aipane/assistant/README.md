# AIPane Assistant API V2

## Overview

The Assistant API V2 is a ground-up rebuild of the chat system using `assistant-ui` and Vercel AI SDK. It provides a simplified, atomic approach to conversation management with built-in streaming, tool usage, and metadata persistence.

## Key Features

- **Atomic Persistence**: All conversation data is saved in a single database transaction
- **Streaming Response**: Real-time streaming with tool call visibility
- **Simplified Context**: Character-based input limits and message round limits
- **Metadata Tracking**: Comprehensive usage analytics and execution step tracking
- **Cache Invalidation**: Automatic UI updates via Next.js revalidation

## API Endpoints

### POST `/api/aipane/assistant`

Streams a chat response using the specified conversation context.

**Request Body:**

```typescript
{
  conversationId: string        // Must start with 'thread_'
  messages: CoreMessage[]       // Chat history
  metadata?: {
    contextIds?: string[]       // Document IDs for context
  }
  model?: string               // AI model (default: gpt-4.1)
  settings?: Record<string, unknown>  // Additional settings
}
```

**Response:**
Streaming response with real-time tool calls and execution steps.

## Configuration

All configuration is centralized in `config.ts`:

- **RATE_LIMIT_CONFIG**: API rate limiting (5 requests/minute)
- **MODEL_CONFIG**: AI model settings (10K token output limit)
- **CONTEXT_CONFIG**: Input/conversation limits (4K chars, 7 assistant messages)
- **RETRY_CONFIG**: Persistence retry settings

## Architecture

### Core Components

1. **Route Handler** (`route.ts`): Main API endpoint with streaming setup
2. **Context Builder** (`utils.ts`): Message history and context management
3. **Persistence Service** (`persistence-service.ts`): Atomic database operations
4. **Configuration** (`config.ts`): Centralized constants and limits

### Why streamText over streamUI?

We intentionally use `streamText` instead of `streamUI` because:

- The frontend uses `assistant-ui` for UI rendering
- We don't need server-side JSX streaming
- `streamText` provides cleaner separation of concerns
- Better compatibility with the `assistant-ui` runtime

## Usage Examples

### Basic Chat

```typescript
const response = await fetch('/api/aipane/assistant', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    conversationId: 'thread_abc123',
    messages: [{ role: 'user', content: 'Hello!' }],
  }),
})
```

### With Context Documents

```typescript
const response = await fetch('/api/aipane/assistant', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    conversationId: 'thread_abc123',
    messages: [{ role: 'user', content: 'Summarize these documents' }],
    metadata: {
      contextIds: ['doc_1', 'doc_2'],
    },
  }),
})
```

## Frontend Integration

Use with `@assistant-ui/react-ai-sdk`:

```typescript
import { useChatRuntime } from '@assistant-ui/react-ai-sdk'

const runtime = useChatRuntime({
  api: '/api/aipane/assistant',
  body: {
    conversationId: 'thread_abc123',
    metadata: { contextIds: ['doc_1'] },
  },
})
```

## Limitations & TODOs

- **Context Documents**: Currently placeholder - needs real document store integration
- **Input Clearing**: Frontend input clearing needs runtime event support
- **Model Configuration**: Consider making model configurable via environment variables

## Testing

Enable Chat V2 in `app/configs/features.ts`:

```typescript
export const FEATURES = {
  ENABLE_CHAT_V2: true,
}
```

Then access: `/aipane?conversation=thread_abc123`
