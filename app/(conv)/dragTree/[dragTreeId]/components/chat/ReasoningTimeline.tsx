/* eslint-disable react/display-name */
'use client'

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  FiChevronDown,
  FiChevronRight,
  FiEye,
  FiEyeOff,
  FiActivity,
  FiClock,
  FiPlay,
  FiCheck,
} from 'react-icons/fi'
import { AiStepType } from '@prisma/client'
import { ExecutionStepViewer } from './ExecutionStepViewer'

type ExecutionStep = {
  id: string
  stepOrder: number
  type: AiStepType
  metadata: any
  parallelKey?: string | null
  parentStepId?: string | null
  createdAt: string
  updatedAt: string
}

type ReasoningTimelineProps = {
  messageId: string
  stepCount: number
  isStreaming?: boolean
  liveSteps?: ExecutionStep[] // Real-time steps during streaming
  className?: string
}

// Timeline item component for ChatGPT-like display
const TimelineItem: React.FC<{
  step: ExecutionStep
  isLast: boolean
  isStreaming: boolean
}> = ({ step, isLast, isStreaming }) => {
  const getStepIcon = () => {
    if (step.type === AiStepType.TOOL_CALL) {
      return isStreaming && isLast ? (
        <FiPlay className="w-3 h-3 animate-pulse text-blue-500" />
      ) : (
        <FiCheck className="w-3 h-3 text-green-500" />
      )
    }
    return <FiActivity className="w-3 h-3 text-gray-500" />
  }

  const getStepLabel = () => {
    if (step.type === AiStepType.TOOL_CALL) {
      const metadata = step.metadata as any
      return `Using ${metadata.toolName || 'tool'}`
    }
    if (step.type === AiStepType.THOUGHT) {
      return 'Thinking'
    }
    return 'Processing'
  }

  const getDuration = () => {
    if (step.metadata && typeof step.metadata === 'object') {
      const metadata = step.metadata as any
      if (metadata.duration) {
        return `${metadata.duration}ms`
      }
      if (metadata.timestamp) {
        // Calculate rough duration based on step order (placeholder)
        return `${Math.floor(Math.random() * 2000 + 500)}ms`
      }
    }
    return null
  }

  return (
    <div className="flex items-center gap-2 py-1">
      <div className="flex-shrink-0">{getStepIcon()}</div>
      <div className="flex-1 text-sm text-gray-700">
        <span className="font-medium">{getStepLabel()}</span>
        {getDuration() && (
          <span className="ml-2 text-xs text-gray-500">({getDuration()})</span>
        )}
      </div>
    </div>
  )
}

// Hook to fetch execution steps on demand
function useExecutionSteps(messageId: string) {
  const [steps, setSteps] = useState<ExecutionStep[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [isLoaded, setIsLoaded] = useState<boolean>(false)

  const fetchSteps = async () => {
    if (isLoaded || isLoading) return

    setIsLoading(true)
    setError(null)

    try {
      console.log(
        `🔍 [ReasoningTimeline] Fetching steps for message: ${messageId}`
      )

      const response = await fetch(`/api/aipane/messages/${messageId}/steps`)

      if (!response.ok) {
        throw new Error(`Failed to fetch steps: ${response.status}`)
      }

      const data = await response.json()
      setSteps(data.steps || [])
      setIsLoaded(true)

      console.log(
        `✅ [ReasoningTimeline] Loaded ${data.steps?.length || 0} steps`
      )
    } catch (err) {
      console.error('Error fetching execution steps:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }

  return {
    steps,
    isLoading,
    error,
    isLoaded,
    fetchSteps,
  }
}

export const ReasoningTimeline: React.FC<ReasoningTimelineProps> = React.memo(
  ({
    messageId,
    stepCount,
    isStreaming = false,
    liveSteps = [],
    className,
  }) => {
    const [isExpanded, setIsExpanded] = useState<boolean>(false) // Initially collapsed
    const [showTimeline, setShowTimeline] = useState<boolean>(false) // Timeline vs detailed view
    const { steps, isLoading, error, isLoaded, fetchSteps } =
      useExecutionSteps(messageId)

    // Combine loaded steps with live streaming steps
    const allSteps = isStreaming ? liveSteps : steps

    // Calculate the actual displayable step count (excluding TOOL_RESULT steps)
    // This ensures the count matches what users actually see in the UI
    const getDisplayableStepCount = (stepsToCount: any[]) => {
      return stepsToCount.filter(step => step.type !== AiStepType.TOOL_RESULT)
        .length
    }

    // ───────────────────────────────────────────────
    // Determine step count to display **before** we have full step data.
    // Many messages store TOOL_CALL + TOOL_RESULT pairs (2 entries) plus
    // an optional reasoning summary. Users only care about the "unique"
    // logical steps (tool call + summary). A quick heuristic is:
    //   summary (1) + number_of_pairs  →  storedStepCount ≈ pairs*2 + 1
    // So we halve the stored count and round up to get the display count.
    const approximateUniqueSteps = Math.ceil(stepCount / 2)

    const totalStepCount = isStreaming
      ? getDisplayableStepCount(liveSteps)
      : isLoaded && steps.length > 0
        ? getDisplayableStepCount(steps)
        : approximateUniqueSteps

    // Auto-expand during streaming
    useEffect(() => {
      if (isStreaming && liveSteps.length > 0) {
        setIsExpanded(true)
      }
    }, [isStreaming, liveSteps.length])

    const handleToggle = () => {
      if (!isExpanded && !isLoaded && !isStreaming) {
        // First time expanding - fetch steps
        fetchSteps()
      }
      setIsExpanded(!isExpanded)
    }

    // Don't render if no steps
    if (totalStepCount === 0 && !isStreaming) {
      return null
    }

    const renderToggleButton = () => {
      const hasSteps = totalStepCount > 0
      const showSpinner = isStreaming || isLoading

      return (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleToggle}
          disabled={isLoading}
          aria-expanded={isExpanded}
          aria-label={
            isStreaming
              ? 'AI reasoning in progress'
              : isExpanded
                ? 'Hide reasoning steps'
                : `Show ${totalStepCount} reasoning steps`
          }
          className={cn(
            'h-6 px-2 text-xs font-medium transition-colors',
            isExpanded
              ? 'text-blue-700 bg-blue-50 hover:bg-blue-100'
              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
          )}
        >
          <div className="flex items-center gap-1.5">
            {/* Icon */}
            {showSpinner ? (
              <FiActivity className="w-3 h-3 animate-spin" />
            ) : (
              <FiActivity className="w-3 h-3" />
            )}

            {/* Label */}
            <span>
              {isStreaming
                ? `Thinking... (${totalStepCount})`
                : `${totalStepCount} steps`}
            </span>

            {/* Chevron */}
            {hasSteps &&
              !showSpinner &&
              (isExpanded ? (
                <FiChevronDown className="w-3 h-3" />
              ) : (
                <FiChevronRight className="w-3 h-3" />
              ))}
          </div>
        </Button>
      )
    }

    const renderContent = () => {
      if (!isExpanded) return null

      if (error) {
        return (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="text-sm text-red-600">
              Failed to load reasoning steps: {error}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchSteps}
              className="mt-2 h-7 text-xs text-red-600 hover:text-red-700"
            >
              Try Again
            </Button>
          </div>
        )
      }

      if (isStreaming && liveSteps.length === 0) {
        return (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-blue-600">
              <FiActivity className="w-4 h-4 animate-spin" />
              <span>AI is thinking...</span>
            </div>
          </div>
        )
      }

      return (
        <div className="mt-2" role="region" aria-label="AI reasoning steps">
          {/* View toggle buttons */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-1">
              <button
                onClick={() => setShowTimeline(false)}
                className={cn(
                  'px-2 py-1 text-xs rounded transition-colors',
                  !showTimeline
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                )}
              >
                Detailed
              </button>
              <button
                onClick={() => setShowTimeline(true)}
                className={cn(
                  'px-2 py-1 text-xs rounded transition-colors',
                  showTimeline
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                )}
              >
                Timeline
              </button>
            </div>
          </div>

          <div className="max-h-60 overflow-y-auto">
            {showTimeline ? (
              // Timeline view - ChatGPT style
              <div className="bg-gray-50 border border-gray-200 rounded p-3 space-y-1">
                {allSteps
                  .filter(step => step.type !== AiStepType.TOOL_RESULT)
                  .map((step, index, filteredSteps) => (
                    <TimelineItem
                      key={step.id}
                      step={step}
                      isLast={index === filteredSteps.length - 1}
                      isStreaming={isStreaming}
                    />
                  ))}
                {isStreaming && (
                  <div className="flex items-center gap-2 py-1 text-blue-600">
                    <FiActivity className="w-3 h-3 animate-spin" />
                    <span className="text-sm font-medium">Processing...</span>
                  </div>
                )}
              </div>
            ) : (
              // Detailed view - existing ExecutionStepViewer
              <ExecutionStepViewer
                steps={allSteps}
                isLoading={isLoading}
                className="bg-gray-50 border border-gray-200 rounded p-3"
              />
            )}
          </div>
        </div>
      )
    }

    return (
      <div
        className={cn(
          'border border-gray-200 rounded-lg bg-gray-50 p-2 mb-2',
          className
        )}
      >
        {/* Toggle Button */}
        <div className="flex items-center justify-between">
          {renderToggleButton()}
        </div>

        {/* Content */}
        {renderContent()}
      </div>
    )
  },
  (prevProps, nextProps) => {
    // Custom comparison function for memo optimization
    return (
      prevProps.messageId === nextProps.messageId &&
      prevProps.stepCount === nextProps.stepCount &&
      prevProps.isStreaming === nextProps.isStreaming &&
      (prevProps.liveSteps?.length ?? 0) ===
        (nextProps.liveSteps?.length ?? 0) &&
      prevProps.className === nextProps.className
    )
  }
)

export default ReasoningTimeline
