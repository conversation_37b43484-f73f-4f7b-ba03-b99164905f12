'use client'

import React, { Component, ReactNode } from 'react'
import { TreeNode } from '@/app/types'
import {
  validateTreeStructure,
  sanitizeTreeStructure,
} from '@/app/stores/dragtree_store/utils/tree-validation'
import { performanceMonitor } from '@/app/utils/performance-monitor'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
  onRecovery?: (recoveredTree: TreeNode | null) => void
  lastKnownGoodTree?: TreeNode | null
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
  recoveryAttempts: number
  isRecovering: boolean
  recoveredTree: TreeNode | null
}

/**
 * Error Boundary for Tree Components with Recovery Mechanisms
 *
 * Features:
 * - Catches tree-related errors and prevents app crashes
 * - Attempts automatic recovery using validation and sanitization
 * - Falls back to last known good state
 * - Provides user-friendly error messages and recovery options
 * - Tracks error patterns for debugging
 */
export class TreeErrorBoundary extends Component<Props, State> {
  private maxRecoveryAttempts = 3
  private recoveryTimeout: NodeJS.Timeout | null = null

  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      recoveryAttempts: 0,
      isRecovering: false,
      recoveredTree: null,
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const endTiming = performanceMonitor.startTiming('errorBoundary.catch')

    console.error('🚨 [TreeErrorBoundary] Caught error:', error)
    console.error('🚨 [TreeErrorBoundary] Error info:', errorInfo)

    this.setState({
      error,
      errorInfo,
      recoveryAttempts: 0,
    })

    // Record error metrics
    endTiming({ errorCount: 1 })

    // Notify parent component
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // Attempt automatic recovery
    this.attemptRecovery()
  }

  /**
   * Attempt to recover from the error using various strategies
   */
  private attemptRecovery = async () => {
    const { recoveryAttempts } = this.state
    const { lastKnownGoodTree } = this.props

    if (recoveryAttempts >= this.maxRecoveryAttempts) {
      console.error('🚨 [TreeErrorBoundary] Max recovery attempts reached')
      return
    }

    this.setState({
      isRecovering: true,
      recoveryAttempts: recoveryAttempts + 1,
    })

    const endTiming = performanceMonitor.startTiming('errorBoundary.recovery')

    try {
      console.log(
        `🔧 [TreeErrorBoundary] Attempting recovery (attempt ${recoveryAttempts + 1}/${this.maxRecoveryAttempts})`
      )

      let recoveredTree: TreeNode | null = null

      // Recovery Strategy 1: Validate and sanitize last known good tree
      if (lastKnownGoodTree) {
        console.log(
          '🔧 [TreeErrorBoundary] Trying recovery with last known good tree'
        )

        const validation = validateTreeStructure(lastKnownGoodTree)
        if (validation.isValid) {
          recoveredTree = lastKnownGoodTree
          console.log('✅ [TreeErrorBoundary] Last known good tree is valid')
        } else {
          console.log(
            '🔧 [TreeErrorBoundary] Last known good tree has issues, attempting sanitization'
          )
          recoveredTree = sanitizeTreeStructure(lastKnownGoodTree)

          if (recoveredTree) {
            const sanitizedValidation = validateTreeStructure(recoveredTree)
            if (!sanitizedValidation.isValid) {
              console.warn(
                '⚠️ [TreeErrorBoundary] Sanitized tree still has issues'
              )
              recoveredTree = null
            }
          }
        }
      }

      // Recovery Strategy 2: Create minimal valid tree structure
      if (!recoveredTree) {
        console.log('🔧 [TreeErrorBoundary] Creating minimal fallback tree')
        recoveredTree = this.createFallbackTree()
      }

      if (recoveredTree) {
        endTiming({ nodeCount: 1 })

        this.setState({
          recoveredTree,
          isRecovering: false,
        })

        console.log('✅ [TreeErrorBoundary] Recovery successful')

        // Notify parent of recovery
        if (this.props.onRecovery) {
          this.props.onRecovery(recoveredTree)
        }

        // Auto-retry rendering after a short delay
        this.recoveryTimeout = setTimeout(() => {
          this.handleRetry()
        }, 1000)
      } else {
        throw new Error('All recovery strategies failed')
      }
    } catch (recoveryError) {
      endTiming({ errorCount: 1 })

      console.error('🚨 [TreeErrorBoundary] Recovery failed:', recoveryError)
      this.setState({ isRecovering: false })

      // Try again after a delay if we haven't exceeded max attempts
      if (recoveryAttempts + 1 < this.maxRecoveryAttempts) {
        this.recoveryTimeout = setTimeout(
          () => {
            this.attemptRecovery()
          },
          2000 * (recoveryAttempts + 1)
        ) // Exponential backoff
      }
    }
  }

  /**
   * Create a minimal valid tree structure as last resort
   */
  private createFallbackTree = (): TreeNode => {
    return {
      id: `fallback_${Date.now()}`,
      label: 'Recovery Mode - Tree Structure Restored',
      type: 'category',
      children: [
        {
          id: `fallback_child_${Date.now()}`,
          label:
            'Your tree data is being recovered. Please refresh if issues persist.',
          type: 'question',
          children: [],
          isInterestedIn: false,
        },
      ],
      isInterestedIn: false,
    }
  }

  /**
   * Handle manual retry from user
   */
  private handleRetry = () => {
    console.log('🔄 [TreeErrorBoundary] Manual retry triggered')

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      isRecovering: false,
      recoveryAttempts: 0,
      recoveredTree: null,
    })

    // Clear any pending recovery timeout
    if (this.recoveryTimeout) {
      clearTimeout(this.recoveryTimeout)
      this.recoveryTimeout = null
    }
  }

  /**
   * Handle manual refresh
   */
  private handleRefresh = () => {
    console.log('🔄 [TreeErrorBoundary] Manual refresh triggered')
    window.location.reload()
  }

  componentWillUnmount() {
    if (this.recoveryTimeout) {
      clearTimeout(this.recoveryTimeout)
    }
  }

  render() {
    const { hasError, error, isRecovering, recoveryAttempts, recoveredTree } =
      this.state
    const { children, fallback } = this.props

    if (hasError) {
      // Show custom fallback if provided
      if (fallback) {
        return fallback
      }

      // Show recovery UI
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
            <div className="text-center">
              <div className="text-red-500 text-4xl mb-4">⚠️</div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Tree Structure Error
              </h2>
              <p className="text-gray-600 mb-4">
                {isRecovering
                  ? `Attempting automatic recovery... (${recoveryAttempts}/${this.maxRecoveryAttempts})`
                  : 'An error occurred while rendering the tree structure.'}
              </p>

              {error && (
                <details className="text-left bg-gray-100 rounded p-3 mb-4">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700">
                    Error Details
                  </summary>
                  <pre className="text-xs text-gray-600 mt-2 whitespace-pre-wrap">
                    {error.message}
                  </pre>
                </details>
              )}

              {recoveredTree && (
                <div className="bg-green-50 border border-green-200 rounded p-3 mb-4">
                  <p className="text-sm text-green-700">
                    ✅ Recovery data prepared. Click "Try Again" to restore your
                    tree.
                  </p>
                </div>
              )}

              <div className="space-y-2">
                <button
                  onClick={this.handleRetry}
                  disabled={isRecovering}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isRecovering ? 'Recovering...' : 'Try Again'}
                </button>

                <button
                  onClick={this.handleRefresh}
                  className="w-full bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700 transition-colors"
                >
                  Refresh Page
                </button>
              </div>

              <p className="text-xs text-gray-500 mt-4">
                If this problem persists, please contact support with the error
                details above.
              </p>
            </div>
          </div>
        </div>
      )
    }

    return children
  }
}
