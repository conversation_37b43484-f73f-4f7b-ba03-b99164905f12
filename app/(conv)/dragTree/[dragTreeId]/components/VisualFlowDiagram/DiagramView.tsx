'use client'
import React, { useEffect, useRef, useCallback, Suspense, lazy } from 'react'
import {
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  useReactFlow,
  useNodesInitialized,
} from 'reactflow'
import type { OnMove, Viewport, Node as FlowNode } from 'reactflow'
import 'reactflow/dist/style.css'

import { useDiagramData } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useDiagramData'
import { useDiagramNavigation } from './hooks/useDiagramNavigation'
import { useFlowImageDownload } from './hooks/useFlowImageDownload'
import { nodeTypes } from './nodes/index'
import { LayoutModeToggle } from './LayoutModeToggle'
import { cn } from '@/lib/utils'

// Dynamically import ReactFlow main component to reduce initial bundle size
// This saves ~300KB+ from initial bundle since ReactFlow is only loaded when needed
const ReactFlow = lazy(() =>
  import('reactflow').then(module => ({
    default: module.default,
  }))
)

// Loading fallback component for ReactFlow
const ReactFlowSkeleton: React.FC = () => (
  <div className="w-full h-full flex items-center justify-center bg-gray-50 rounded-lg">
    <div className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
      <p className="text-gray-600 text-sm">Loading diagram...</p>
    </div>
  </div>
)

type DiagramViewProps = {
  layoutMode: 'linear' | 'radial'
  setLayoutMode: (mode: 'linear' | 'radial') => void
}

// Internal DiagramView component that uses ReactFlow hooks
const DiagramViewInternal: React.FC<DiagramViewProps> = React.memo(
  function DiagramViewInternal({ layoutMode, setLayoutMode }) {
    const { diagramData, layoutPromise } = useDiagramData(layoutMode)
    const [nodes, setNodes, onNodesChange] = useNodesState([])
    const [edges, setEdges, onEdgesChange] = useEdgesState([])
    const { fitView, setViewport, getNodes } = useReactFlow()
    const nodesInitialized = useNodesInitialized()
    const { downloadImage } = useFlowImageDownload(layoutMode, setLayoutMode)
    const initialFocusRef = useRef<boolean>(false)
    const currentLayoutModeRef = useRef<'linear' | 'radial'>(layoutMode)
    const lastValidViewportRef = useRef<Viewport>({ x: 0, y: 0, zoom: 0.5 })
    const recoveryTimeoutRef = useRef<NodeJS.Timeout | null>(null)
    const focusTimeoutRef = useRef<NodeJS.Timeout | null>(null)
    const [minimapError, setMinimapError] = React.useState<boolean>(false)
    const [radialViewReady, setRadialViewReady] = React.useState<boolean>(
      layoutMode !== 'radial'
    )

    /**
     * Reliably identifies the root node using multiple fallback strategies
     * Returns the root node or null if not found
     */
    const findRootNode = useCallback(
      (nodesToSearch: any[]) => {
        if (!nodesToSearch || nodesToSearch.length === 0) return null

        // Strategy 1: Find node with level 1 (most reliable)
        let rootNode = nodesToSearch.find((n: any) => n.data?.level === 1)
        if (rootNode) return rootNode

        // Strategy 2: Find node without parentId (fallback)
        rootNode = nodesToSearch.find((n: any) => !n.data?.parentId)
        if (rootNode) return rootNode

        // Strategy 3: Find node that's not a target of any edge (ultimate fallback)
        const targetNodeIds = new Set(
          (edges || []).map((edge: any) => edge.target)
        )
        rootNode = nodesToSearch.find((n: any) => !targetNodeIds.has(n.id))
        if (rootNode) return rootNode

        // Strategy 4: Just use the first node (last resort)
        return nodesToSearch[0] || null
      },
      [edges]
    )

    /**
     * Validates if nodes are properly positioned by ELK.js radial layout
     * Returns true if nodes have valid, spread-out positions
     */
    const areNodesProperlyPositioned = useCallback((nodesToCheck: any[]) => {
      if (nodesToCheck.length === 0) return false

      // Check if nodes have valid positions
      const validPositions = nodesToCheck.filter(
        (node: any) =>
          isFinite(node.position.x) &&
          isFinite(node.position.y) &&
          !isNaN(node.position.x) &&
          !isNaN(node.position.y)
      )

      if (validPositions.length === 0) return false

      // For circular layout, be more strict - require more nodes to be positioned
      // and check for better distribution (not just clustered at origin)
      const wellPositioned = validPositions.filter(
        (node: any) =>
          Math.abs(node.position.x) > 50 || Math.abs(node.position.y) > 50
      )

      // Need at least 70% of nodes to be well-positioned for circular layout
      const positionedRatio = wellPositioned.length / validPositions.length
      return positionedRatio > 0.7
    }, [])

    /**
     * Centralized function to focus on the root node with proper timing and fallbacks
     * Handles both hierarchical and radial layouts consistently
     */
    const focusOnRootNode = useCallback(
      (nodesToFocus: any[], attempt: number = 0) => {
        // Clear any existing focus timeout
        if (focusTimeoutRef.current) {
          clearTimeout(focusTimeoutRef.current)
          focusTimeoutRef.current = null
        }

        // Prevent multiple simultaneous focus attempts
        if (initialFocusRef.current) {
          console.log('🎯 [DiagramView] Root focus already completed, skipping')
          return
        }

        const maxAttempts = 5
        const baseDelay = layoutMode === 'radial' ? 200 : 100 // Radial needs more time
        const delay = Math.min(baseDelay * Math.pow(1.5, attempt), 2000) // Exponential backoff

        console.log(
          `🎯 [DiagramView] Attempting root focus (attempt ${attempt + 1}/${maxAttempts})`
        )

        const rootNode = findRootNode(nodesToFocus)
        if (!rootNode) {
          console.warn('🎯 [DiagramView] No root node found for focusing')
          return
        }

        // For radial layout, check if nodes are properly positioned
        if (
          layoutMode === 'radial' &&
          !areNodesProperlyPositioned(nodesToFocus)
        ) {
          if (attempt < maxAttempts - 1) {
            console.log(
              `🎯 [DiagramView] Radial nodes not ready, retrying in ${delay}ms`
            )
            focusTimeoutRef.current = setTimeout(() => {
              focusOnRootNode(getNodes(), attempt + 1)
            }, delay)
            return
          } else {
            console.warn(
              '🎯 [DiagramView] Max attempts reached, focusing anyway'
            )
          }
        }

        try {
          if (layoutMode === 'radial') {
            // For radial layout, use general fitView to show the entire layout
            fitView({
              duration: 500,
              padding: 0.3,
              maxZoom: 0.5,
            })
            console.log('🎯 [DiagramView] Applied radial fitView')
          } else {
            // For hierarchical layout, focus specifically on the root node
            fitView({
              nodes: [{ id: rootNode.id }],
              duration: 500,
              padding: 0.3,
              maxZoom: 0.5,
            })
            console.log(`🎯 [DiagramView] Focused on root node: ${rootNode.id}`)
          }

          initialFocusRef.current = true

          // For radial layout, mark as ready
          if (layoutMode === 'radial') {
            setRadialViewReady(true)
          }
        } catch (error) {
          console.error('🎯 [DiagramView] Focus attempt failed:', error)

          if (attempt < maxAttempts - 1) {
            focusTimeoutRef.current = setTimeout(() => {
              focusOnRootNode(getNodes(), attempt + 1)
            }, delay)
          }
        }
      },
      [layoutMode, findRootNode, areNodesProperlyPositioned, fitView, getNodes]
    )

    /**
     * Legacy function for backward compatibility - now delegates to focusOnRootNode
     * @deprecated Use focusOnRootNode instead
     */
    const attemptCenterWithBackoff = useCallback(
      async (attempt: number = 0) => {
        focusOnRootNode(getNodes(), attempt)
      },
      [focusOnRootNode, getNodes]
    )

    /**
     * Validates viewport values to prevent NaN SVG viewBox errors
     * Returns true if viewport is valid, false if contains NaN/Infinity
     */
    const isValidViewport = useCallback((viewport: Viewport): boolean => {
      const { x, y, zoom } = viewport
      return (
        isFinite(x) &&
        isFinite(y) &&
        isFinite(zoom) &&
        !isNaN(x) &&
        !isNaN(y) &&
        !isNaN(zoom) &&
        zoom > 0 &&
        zoom <= 10 // Reasonable zoom limits
      )
    }, [])

    /**
     * Handles automatic recovery when invalid viewport detected
     * Uses debounced fitView to prevent infinite recovery loops
     */
    const triggerViewportRecovery = useCallback(() => {
      // Clear any existing recovery timeout
      if (recoveryTimeoutRef.current) {
        clearTimeout(recoveryTimeoutRef.current)
      }

      // Debounced recovery to prevent rapid-fire fitView calls
      recoveryTimeoutRef.current = setTimeout(() => {
        console.warn(
          'ReactFlow: Invalid viewport detected, triggering automatic fitView recovery'
        )

        try {
          // Reset minimap error state on recovery attempt
          setMinimapError(false)

          // First try to restore last known good viewport
          if (isValidViewport(lastValidViewportRef.current)) {
            setViewport(lastValidViewportRef.current, { duration: 300 })
          } else {
            // Fallback to fitView if last viewport is also invalid
            fitView({
              duration: 500,
              padding: 0.3,
              maxZoom: 2.0,
              minZoom: 0.1,
            })
          }

          // Reset minimap error after successful recovery
          setTimeout(() => setMinimapError(false), 500)
        } catch (error) {
          console.error(
            'ReactFlow: Recovery failed, attempting fallback fitView:',
            error
          )
          setMinimapError(true) // Mark minimap as potentially broken

          // Final fallback
          setTimeout(() => {
            fitView({ duration: 0 })
            setMinimapError(false) // Reset after final fallback
          }, 100)
        }
      }, 150) // 150ms debounce to prevent rapid recovery
    }, [isValidViewport, setViewport, fitView])

    /**
     * Monitors viewport changes and validates them
     * Triggers recovery if invalid viewport detected
     */
    const handleViewportMove: OnMove = useCallback(
      (_event, viewport) => {
        if (isValidViewport(viewport)) {
          // Store valid viewport as backup
          lastValidViewportRef.current = { ...viewport }
        } else {
          // Invalid viewport detected - trigger recovery
          console.warn('ReactFlow: Invalid viewport values detected:', viewport)
          triggerViewportRecovery()
        }
      },
      [isValidViewport, triggerViewportRecovery]
    )

    useEffect(() => {
      layoutPromise.then(layout => {
        console.log(`🎯 [DiagramView] Layout completed for ${layoutMode} mode`)

        setNodes(layout.nodes || diagramData.nodes)
        setEdges(layout.edges || diagramData.edges)

        // Reset initial focus flag when layout changes
        initialFocusRef.current = false

        // Use centralized focus function with appropriate timing
        const nodesToFocus = layout.nodes || diagramData.nodes
        if (nodesToFocus && nodesToFocus.length > 0) {
          // Give React Flow time to apply the new node positions
          const delay = layoutMode === 'radial' ? 100 : 50
          setTimeout(() => {
            focusOnRootNode(nodesToFocus)
          }, delay)
        }
      })
    }, [
      layoutPromise,
      diagramData,
      setNodes,
      setEdges,
      layoutMode,
      focusOnRootNode,
    ])

    useDiagramNavigation(nodes)

    // Handle node clicks to prevent default React Flow selection behavior
    const handleNodeClick = (event: React.MouseEvent, _node: FlowNode) => {
      // Prevent React Flow's default node selection behavior
      event.preventDefault()
      event.stopPropagation()
      // The actual navigation is handled by the node's own onClick handler
    }

    // Track layout mode changes
    useEffect(() => {
      if (currentLayoutModeRef.current !== layoutMode) {
        console.log(`🎯 [DiagramView] Layout mode changed to: ${layoutMode}`)

        currentLayoutModeRef.current = layoutMode
        initialFocusRef.current = false // Reset focus flag on layout mode change

        // Clear any pending focus timeouts
        if (focusTimeoutRef.current) {
          clearTimeout(focusTimeoutRef.current)
          focusTimeoutRef.current = null
        }

        if (layoutMode === 'radial') {
          setRadialViewReady(false) // Prepare to hide until new fitView is done
        } else {
          setRadialViewReady(true)
        }
      }
    }, [layoutMode])

    // Backup focus attempt when nodes are initialized (safety net)
    // This handles edge cases where the layout promise focus might fail
    useEffect(() => {
      if (nodes.length > 0 && nodesInitialized && !initialFocusRef.current) {
        console.log('🎯 [DiagramView] Backup focus attempt triggered')

        // Small delay to avoid race conditions with layout promise
        setTimeout(() => {
          if (!initialFocusRef.current) {
            console.log('🎯 [DiagramView] Executing backup focus')
            focusOnRootNode(nodes)
          }
        }, 200)
      }
    }, [nodes, nodesInitialized, focusOnRootNode])

    // Listen for download events from header button
    useEffect(() => {
      const handleDownloadEvent = async (event: Event) => {
        const { layoutMode: requestedLayoutMode } =
          (event as CustomEvent).detail || {}

        // Dispatch status events with layout mode info
        window.dispatchEvent(
          new CustomEvent('reactflow-download-start', {
            detail: { layoutMode: requestedLayoutMode },
          })
        )

        try {
          await downloadImage(requestedLayoutMode)
        } finally {
          window.dispatchEvent(
            new CustomEvent('reactflow-download-end', {
              detail: { layoutMode: requestedLayoutMode },
            })
          )
        }
      }

      window.addEventListener('reactflow-download', handleDownloadEvent)

      return () => {
        window.removeEventListener('reactflow-download', handleDownloadEvent)
      }
    }, [downloadImage])

    const hasSentReadyEvent = React.useRef(false)
    useEffect(() => {
      if (!hasSentReadyEvent.current && nodes.length > 0 && nodesInitialized) {
        window.dispatchEvent(new CustomEvent('reactflow-diagram-ready'))
        hasSentReadyEvent.current = true
      }
    }, [nodes, nodesInitialized])

    // Cleanup timeouts and reset error states on unmount
    useEffect(() => {
      return () => {
        if (recoveryTimeoutRef.current) {
          clearTimeout(recoveryTimeoutRef.current)
        }
        if (focusTimeoutRef.current) {
          clearTimeout(focusTimeoutRef.current)
        }
        // Reset error states to prevent persistence between route changes
        setMinimapError(false)
        setRadialViewReady(layoutMode !== 'radial')
      }
    }, [])

    /**
     * MiniMap Error Boundary Component
     * Catches SVG viewBox errors and provides fallback rendering
     */
    const MiniMapWithErrorBoundary: React.FC = () => {
      const [hasError, setHasError] = React.useState<boolean>(false)

      React.useEffect(() => {
        // Reset error state when minimap error state changes
        if (!minimapError) {
          setHasError(false)
        }
      }, [minimapError])

      // Error boundary using error event listener
      React.useEffect(() => {
        const handleError = (event: ErrorEvent) => {
          // Narrow the listener to SVG viewBox related issues only
          if (!event?.message) return

          if (
            event.message.includes('viewBox') ||
            event.message.includes('NaN')
          ) {
            console.warn('MiniMap SVG error detected:', event.message)
            setHasError(true)
            setMinimapError(true)
            // Auto-trigger recovery
            triggerViewportRecovery()
          }
        }

        window.addEventListener('error', handleError)
        return () => window.removeEventListener('error', handleError)
      }, [triggerViewportRecovery])

      if (hasError || minimapError) {
        return (
          <div
            className="opacity-75 hover:opacity-95 transition-all duration-300 shadow-lg hover:shadow-xl"
            style={{
              position: 'absolute',
              bottom: '20px',
              right: '20px',
              width: '150px',
              height: '100px',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              border: '1px solid rgba(0, 0, 0, 0.15)',
              borderRadius: '12px',
              backdropFilter: 'blur(8px)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '12px',
              color: '#64748b',
              cursor: 'pointer',
            }}
            onClick={() => {
              setHasError(false)
              setMinimapError(false)
              triggerViewportRecovery()
            }}
          >
            <div className="text-center">
              <div>MiniMap</div>
              <div>Recovering...</div>
              <div className="text-xs mt-1">Click to retry</div>
            </div>
          </div>
        )
      }

      try {
        return (
          <MiniMap
            nodeColor={n => {
              if (n.type === 'customCategoryNode') return '#9333ea'
              if (n.type === 'customQuestionNode') return '#22c55e'
              return '#cbd5e1'
            }}
            nodeStrokeWidth={3}
            zoomable
            pannable
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.7)',
              border: '1px solid rgba(0, 0, 0, 0.15)',
              borderRadius: '12px',
              backdropFilter: 'blur(8px)',
            }}
            className="opacity-75 hover:opacity-95 transition-all duration-300 shadow-lg hover:shadow-xl"
          />
        )
      } catch (error) {
        console.error('MiniMap render error:', error)
        setHasError(true)
        return null
      }
    }

    /*
     * TRACKPAD PINCH ZOOM DISABLED
     *
     * macOS trackpad pinch gestures are disabled due to a known React Flow bug where
     * pinch gestures can generate extreme zoom values (including NaN/Infinity) that
     * cause SVG rendering errors in Background and MiniMap components.
     *
     * Users can still zoom using:
     * - Mouse scroll wheel (zoomOnScroll={true})
     * - Zoom controls in the UI
     * - MiniMap zoom functionality
     *
     * Related GitHub Issues:
     * - https://github.com/xyflow/xyflow/issues/4056 (Zoom limits bypass)
     * - https://github.com/xyflow/xyflow/issues/5268 (NaN values in transforms)
     *
     * To re-enable in the future: Change zoomOnPinch={false} to zoomOnPinch={true}
     * when React Flow fixes the underlying transform validation issues.
     */

    return (
      <div
        className="h-full w-full relative"
        style={{
          visibility:
            layoutMode === 'radial' && !radialViewReady ? 'hidden' : 'visible',
        }}
      >
        <LayoutModeToggle
          layoutMode={layoutMode}
          setLayoutMode={setLayoutMode}
        />
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onNodeClick={handleNodeClick}
          onMove={handleViewportMove}
          nodeTypes={nodeTypes}
          defaultViewport={{ x: 0, y: 0, zoom: 0.5 }}
          minZoom={0.1}
          maxZoom={3.0}
          translateExtent={[
            [-20000, -20000],
            [20000, 20000],
          ]}
          panOnDrag={true}
          panOnScroll={true}
          zoomOnScroll={true}
          zoomOnPinch={false} // Disabled due to React Flow pinch zoom bug
          zoomOnDoubleClick={false}
          preventScrolling={false}
          onInit={() => {
            // Signal globally that ReactFlow is ready so external components can act
            window.dispatchEvent(new CustomEvent('reactflow-ready'))
          }}
          className={cn(
            'transition-all duration-500',
            layoutMode === 'radial'
              ? 'bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50'
              : 'bg-gradient-to-br from-slate-50 to-stone-100'
          )}
          proOptions={{ hideAttribution: true }}
        >
          <Controls className="react-flow-controls" />
          <MiniMapWithErrorBoundary />
          <Background gap={24} />
        </ReactFlow>
      </div>
    )
  }
)

export const DiagramView: React.FC<DiagramViewProps> = React.memo(
  function DiagramView({ layoutMode, setLayoutMode }) {
    return (
      <Suspense fallback={<ReactFlowSkeleton />}>
        <DiagramViewInternal
          layoutMode={layoutMode}
          setLayoutMode={setLayoutMode}
        />
      </Suspense>
    )
  }
)
