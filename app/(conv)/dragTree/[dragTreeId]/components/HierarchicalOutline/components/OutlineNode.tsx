import React, { useState, useMemo } from 'react'
import { TreeNode } from '@/app/types'
import {
  OutlineViewProps,
  NodeInteractionProps,
} from '@/app/(conv)/dragTree/[dragTreeId]/types'
import { getNodeClasses } from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline/utils'
import NodeContent from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline/components/NodeContent'
import NodeActions from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline/components/NodeActions'
import ConfirmDialog from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline/components/ConfirmDialog'
import QuickResearchPreviewButton from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/QuickResearchPreviewButton'
import QuickResearchDropdownButton from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/QuickResearchDropdownButton'
import {
  DEFAULT_NODE_LABELS,
  TreeNodeType,
} from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'
// import { useDragTreeStore } from '@/app/stores/dragtree_store' // Currently unused
import { cn } from '@/lib/utils'
import NodeCard from './NodeCard'
import CategoryToggleButton from './CategoryToggleButton'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { getTreeNodeAriaAttributes } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useAccessibility'
import { useNavigationStore } from '@/app/stores/navigation_store'

// Combine the props from the main view and the interaction state hook
type CombinedOutlineNodeProps = OutlineViewProps & NodeInteractionProps

const OutlineNode: React.FC<CombinedOutlineNodeProps> = React.memo(
  ({
    node,
    onAdd,
    onDelete,
    onEdit,
    editingNode,
    setEditingNode,
    collapsedNodes,
    setCollapsedNodes,
    targetNodeId,
    onGenerateSimilarQuestions,
    onGenerateSimilarCategories,
    isLoading = false,
    loadingNodeId = '',
    // isGloballyExpanded is unused
    // Interaction state and handlers are now part of the props
    isClicked,
    isHovered,
    isExpanding,
    lastExpansionTime,
    isPersistent,
    handleNodeClick,
    handleToggleCollapse,
    // Drag props passed from SortableOutlineItem
    dragAttributes,
    dragListeners,

    isDragging,
    // onReorder is part of OutlineViewProps but not used directly in this component
    ..._rest
  }) => {
    // const { getNodeContent } = useDragTreeStore() // Currently unused

    // Check if this node has any research content - currently not used in display
    // const nodeContentMap = getNodeContent(node.id)
    // const hasResearchContent = nodeContentMap && nodeContentMap.size > 0

    // FIXED: Use props-based collapsed state instead of creating conflicting local state
    // Get isCollapsed from the props-based collapsedNodes set
    const isCollapsed = collapsedNodes ? collapsedNodes.has(node.id) : false

    // FIXED: Create proper question count function that counts only questions and works always
    const getQuestionCount = (): number => {
      if (node.type !== 'category') return 0
      return node.children.filter(child => child.type === 'question').length
    }

    // Dialog state
    const [isConfirmOpen, setIsConfirmOpen] = useState<boolean>(false)
    const [nodeToDelete, setNodeToDelete] = useState<TreeNode | null>(null)

    // Check if this node is currently being processed
    const isCurrentNodeLoading = isLoading && loadingNodeId === node.id

    // Handler for deletion
    const handleDelete = (currentNode: TreeNode) => {
      setNodeToDelete(currentNode)
      setIsConfirmOpen(true)
    }

    // Confirm deletion
    const confirmDelete = () => {
      if (nodeToDelete) {
        onDelete(nodeToDelete.id, nodeToDelete.type)
        setIsConfirmOpen(false)
        setNodeToDelete(null)
      }
    }

    // Cancel deletion
    const cancelDelete = () => {
      setIsConfirmOpen(false)
      setNodeToDelete(null)
    }

    // Memoize expensive calculations
    const isCategoryWithChildren = useMemo(
      () => node.type === 'category' && node.children.length > 0,
      [node.type, node.children.length]
    )

    // Get direct question children for tooltip - memoized for performance
    const questionLabels = useMemo((): string[] => {
      if (node.type !== 'category') return []
      return node.children
        .filter(child => child.type === 'question')
        .map(child => child.label)
        .filter(label => label !== DEFAULT_NODE_LABELS.NEW_QUESTION)
    }, [node.type, node.children])

    const hasQuestions = questionLabels.length > 0

    // Memoize question count for performance
    const questionCount = useMemo(() => getQuestionCount(), [node.children])

    // Memoize node card props to prevent unnecessary re-renders
    const nodeCardProps = useMemo(
      () => ({
        node,
        isDraggable: true,
        dragAttributes,
        dragListeners,
        isDragging,
      }),
      [node, dragAttributes, dragListeners, isDragging]
    )

    // Memoize expensive computations
    const nodeActions = useMemo(
      () => ({
        onAdd: (nodeId: string, type: TreeNodeType) => onAdd(nodeId, type),
        onEdit: (nodeId: string, newLabel: string) => onEdit(nodeId, newLabel),
        onDelete: (nodeId: string, type: TreeNodeType) => handleDelete(node),
      }),
      [onAdd, onEdit, node, handleDelete]
    )

    const interactionHandlers = useMemo(
      () => ({
        handleNodeClick,
        handleToggleCollapse,
      }),
      [handleNodeClick, handleToggleCollapse]
    )

    // Calculate accessibility attributes with improved accuracy
    const nodeLevel = (node.id.match(/-/g) || []).length + 1 // Estimate level from ID structure

    // Try to determine position and set size from available context
    // While not perfect without parent context, this provides more meaningful values
    const siblingNodes = node.children || []
    const setSize = siblingNodes.length > 0 ? siblingNodes.length : 1

    // For position, we can use a simple heuristic based on the node ID
    // This isn't perfect but is better than hardcoded 1
    const positionInSet = node.id.includes('-')
      ? parseInt(node.id.split('-').pop() || '1', 10) || 1
      : 1

    const ariaAttributes = getTreeNodeAriaAttributes(
      node.type as 'category' | 'question',
      node.type === 'category' ? !isCollapsed : null,
      nodeLevel,
      Math.min(positionInSet, setSize), // Ensure position doesn't exceed set size
      setSize,
      node.label
    )

    const mainContent = (
      <div
        data-node-id={node.id}
        className={getNodeClasses(
          node,
          isHovered,
          isClicked,
          isCurrentNodeLoading,
          isLoading,
          targetNodeId
        )}
        onClick={handleNodeClick}
        {...ariaAttributes}
        onKeyDown={e => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault()
            // Handle keyboard activation by calling the same logic as mouse click
            // but without the mouse event dependency
            const { navigateToNode } = useNavigationStore.getState()
            navigateToNode(node.id)

            // Handle category expansion for keyboard activation
            if (
              node.type === 'category' &&
              node.children.length > 0 &&
              isCollapsed &&
              setCollapsedNodes
            ) {
              const newCollapsedNodes = new Set(collapsedNodes)
              newCollapsedNodes.delete(node.id)
              setCollapsedNodes(newCollapsedNodes)
            }
          }
        }}
      >
        <div className="flex items-center justify-between gap-3">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            {isCategoryWithChildren && (
              <div className="flex items-center gap-1">
                <CategoryToggleButton
                  isCollapsed={isCollapsed}
                  isPersistent={isPersistent}
                  isHovered={isHovered}
                  handleToggleCollapse={handleToggleCollapse}
                />
                {/* FIXED: Always show question count when there are questions, not just when collapsed */}
                {questionCount > 0 && (
                  <div
                    className={cn('transition-all duration-300 ease-in-out', {
                      'opacity-100 scale-100 translate-x-0': true, // Always visible when questions exist
                    })}
                  >
                    <span className="text-xs bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-2.5 py-1 rounded-full font-semibold min-w-[1.5rem] text-center shadow-lg shadow-indigo-200/50">
                      {questionCount}
                    </span>
                  </div>
                )}
              </div>
            )}
            <div
              className={cn('flex-1 min-w-0 transition-all duration-200', {
                'pl-12': !isCategoryWithChildren && node.type === 'category',
              })}
            >
              <div className="flex items-center gap-2">
                {/* Add ResearchButton to the left of question text */}
                {node.type === 'question' &&
                  node.label !== DEFAULT_NODE_LABELS.NEW_QUESTION && (
                    <QuickResearchDropdownButton
                      questionText={node.label}
                      questionNodeId={node.id}
                      variant="treeview"
                      className="flex-shrink-0"
                    />
                  )}
                <div className="flex-1 min-w-0">
                  <NodeContent
                    node={node}
                    editingNode={editingNode}
                    onEdit={onEdit}
                    setEditingNode={setEditingNode}
                    isCurrentNodeLoading={isCurrentNodeLoading}
                    targetNodeId={targetNodeId}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex-shrink-0 flex items-center gap-2">
            <NodeActions
              node={node}
              onAdd={nodeActions.onAdd}
              onEdit={() => setEditingNode(node.id)}
              onDelete={() =>
                nodeActions.onDelete(node.id, node.type as TreeNodeType)
              }
              onGenerateSimilarQuestions={onGenerateSimilarQuestions}
              onGenerateSimilarCategories={onGenerateSimilarCategories}
              isLoading={isLoading}
              isHovered={isHovered}
              isExpanding={isExpanding}
              lastExpansionTime={lastExpansionTime}
            />
          </div>
        </div>
        {node.type === 'question' &&
          node.label !== DEFAULT_NODE_LABELS.NEW_QUESTION && (
            <div className="mt-2 pl-2 pr-1" onClick={e => e.stopPropagation()}>
              <QuickResearchPreviewButton
                nodeId={node.id}
                questionText={node.label}
                className="border-t border-slate-100 pt-3"
                constrainHeight={false}
              />
            </div>
          )}
      </div>
    )

    return (
      <>
        {node.type === 'category' && hasQuestions ? (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <NodeCard {...nodeCardProps}>{mainContent}</NodeCard>
              </TooltipTrigger>
              <TooltipContent side="right" className="max-w-xs">
                <div className="space-y-1">
                  <div className="font-medium text-xs text-gray-600 mb-1">
                    Questions ({questionLabels.length}):
                  </div>
                  {questionLabels.slice(0, 5).map((label, index) => (
                    <div key={index} className="text-xs text-gray-800 truncate">
                      • {label}
                    </div>
                  ))}
                  {questionLabels.length > 5 && (
                    <div className="text-xs text-gray-500 italic">
                      ... and {questionLabels.length - 5} more
                    </div>
                  )}
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : (
          <NodeCard {...nodeCardProps}>{mainContent}</NodeCard>
        )}

        <ConfirmDialog
          isOpen={isConfirmOpen}
          nodeToDelete={nodeToDelete}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />
      </>
    )
  },
  (prevProps, nextProps) => {
    // Custom comparison for better performance
    return (
      prevProps.node.id === nextProps.node.id &&
      prevProps.node.label === nextProps.node.label &&
      prevProps.isLoading === nextProps.isLoading &&
      prevProps.editingNode === nextProps.editingNode &&
      prevProps.targetNodeId === nextProps.targetNodeId &&
      // Add collapsedNodes comparison to ensure re-render when collapse state changes
      prevProps.collapsedNodes === nextProps.collapsedNodes
    )
  }
)

OutlineNode.displayName = 'OutlineNode'

export default OutlineNode
